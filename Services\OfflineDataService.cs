using POSSystem.Models;
using Newtonsoft.Json;
using System.Collections.Concurrent;

namespace POSSystem.Services
{
    /// <summary>
    /// خدمة إدارة البيانات بدون اتصال
    /// </summary>
    public interface IOfflineDataService
    {
        /// <summary>
        /// حفظ فاتورة بدون اتصال
        /// </summary>
        Task SaveOfflineInvoiceAsync(Invoice invoice);
        
        /// <summary>
        /// الحصول على الفواتير المحفوظة بدون اتصال
        /// </summary>
        Task<List<Invoice>> GetOfflineInvoicesAsync();
        
        /// <summary>
        /// إرسال الفواتير المحفوظة عند توفر الاتصال
        /// </summary>
        Task<bool> SyncOfflineDataAsync();
        
        /// <summary>
        /// حذف فاتورة من التخزين المؤقت
        /// </summary>
        Task RemoveOfflineInvoiceAsync(string invoiceNumber);
        
        /// <summary>
        /// التحقق من حالة الاتصال
        /// </summary>
        Task<bool> IsOnlineAsync();
        
        /// <summary>
        /// عدد الفواتير المعلقة
        /// </summary>
        Task<int> GetPendingInvoicesCountAsync();
    }

    public class OfflineDataService : IOfflineDataService
    {
        private readonly IApiService _apiService;
        private readonly string _offlineDataPath;
        private readonly string _pendingInvoicesPath;
        private readonly ConcurrentQueue<Invoice> _pendingInvoices;
        private readonly System.Threading.Timer _syncTimer;
        private bool _isSyncing = false;

        public OfflineDataService(IApiService apiService)
        {
            _apiService = apiService;
            
            var appDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "POSSystem");
            Directory.CreateDirectory(appDataPath);
            
            _offlineDataPath = Path.Combine(appDataPath, "OfflineData");
            Directory.CreateDirectory(_offlineDataPath);
            
            _pendingInvoicesPath = Path.Combine(_offlineDataPath, "PendingInvoices");
            Directory.CreateDirectory(_pendingInvoicesPath);
            
            _pendingInvoices = new ConcurrentQueue<Invoice>();
            
            // تحميل الفواتير المعلقة عند بدء التشغيل
            LoadPendingInvoices();
            
            // تشغيل مؤقت للمزامنة كل دقيقة
            _syncTimer = new System.Threading.Timer(async _ => await TrySyncAsync(), null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
        }

        public async Task SaveOfflineInvoiceAsync(Invoice invoice)
        {
            try
            {
                // إضافة طابع زمني للفاتورة
                invoice.CreatedAt = DateTime.Now;
                
                // حفظ الفاتورة في ملف منفصل
                var fileName = $"invoice_{invoice.InvoiceNumber}_{DateTime.Now:yyyyMMddHHmmss}.json";
                var filePath = Path.Combine(_pendingInvoicesPath, fileName);
                
                var json = JsonConvert.SerializeObject(invoice, Formatting.Indented);
                await File.WriteAllTextAsync(filePath, json);
                
                // إضافة للقائمة المؤقتة
                _pendingInvoices.Enqueue(invoice);
                
                // محاولة إرسال فوري إذا كان هناك اتصال
                _ = Task.Run(async () => await TrySyncAsync());
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ
                await LogErrorAsync($"خطأ في حفظ الفاتورة بدون اتصال: {ex.Message}");
            }
        }

        public async Task<List<Invoice>> GetOfflineInvoicesAsync()
        {
            var invoices = new List<Invoice>();
            
            try
            {
                var files = Directory.GetFiles(_pendingInvoicesPath, "invoice_*.json");
                
                foreach (var file in files)
                {
                    try
                    {
                        var json = await File.ReadAllTextAsync(file);
                        var invoice = JsonConvert.DeserializeObject<Invoice>(json);
                        
                        if (invoice != null)
                        {
                            invoices.Add(invoice);
                        }
                    }
                    catch (Exception ex)
                    {
                        await LogErrorAsync($"خطأ في قراءة الفاتورة {file}: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                await LogErrorAsync($"خطأ في تحميل الفواتير المحفوظة: {ex.Message}");
            }
            
            return invoices.OrderByDescending(i => i.CreatedAt).ToList();
        }

        public async Task<bool> SyncOfflineDataAsync()
        {
            if (_isSyncing) return false;
            
            _isSyncing = true;
            var successCount = 0;
            var totalCount = 0;
            
            try
            {
                var offlineInvoices = await GetOfflineInvoicesAsync();
                totalCount = offlineInvoices.Count;
                
                if (totalCount == 0) return true;
                
                // التحقق من الاتصال
                if (!await IsOnlineAsync()) return false;
                
                foreach (var invoice in offlineInvoices)
                {
                    try
                    {
                        // محاولة إرسال الفاتورة
                        var result = await _apiService.PostAsync<Invoice>("invoices", invoice);
                        
                        if (result != null)
                        {
                            // نجح الإرسال، حذف الملف المحلي
                            await RemoveOfflineInvoiceAsync(invoice.InvoiceNumber);
                            successCount++;
                            
                            await LogInfoAsync($"تم إرسال الفاتورة {invoice.InvoiceNumber} بنجاح");
                        }
                        else
                        {
                            await LogErrorAsync($"فشل في إرسال الفاتورة {invoice.InvoiceNumber}");
                        }
                    }
                    catch (Exception ex)
                    {
                        await LogErrorAsync($"خطأ في إرسال الفاتورة {invoice.InvoiceNumber}: {ex.Message}");
                    }
                    
                    // تأخير قصير بين الطلبات
                    await Task.Delay(500);
                }
                
                await LogInfoAsync($"تم إرسال {successCount} من أصل {totalCount} فاتورة");
                return successCount == totalCount;
            }
            catch (Exception ex)
            {
                await LogErrorAsync($"خطأ في مزامنة البيانات: {ex.Message}");
                return false;
            }
            finally
            {
                _isSyncing = false;
            }
        }

        public async Task RemoveOfflineInvoiceAsync(string invoiceNumber)
        {
            try
            {
                var files = Directory.GetFiles(_pendingInvoicesPath, $"invoice_{invoiceNumber}_*.json");
                
                foreach (var file in files)
                {
                    File.Delete(file);
                }
            }
            catch (Exception ex)
            {
                await LogErrorAsync($"خطأ في حذف الفاتورة {invoiceNumber}: {ex.Message}");
            }
        }

        public async Task<bool> IsOnlineAsync()
        {
            try
            {
                // محاولة ping بسيط للخادم
                var result = await _apiService.GetAsync<object>("health");
                return result != null;
            }
            catch
            {
                return false;
            }
        }

        public async Task<int> GetPendingInvoicesCountAsync()
        {
            try
            {
                var files = Directory.GetFiles(_pendingInvoicesPath, "invoice_*.json");
                return files.Length;
            }
            catch
            {
                return 0;
            }
        }

        private void LoadPendingInvoices()
        {
            try
            {
                var files = Directory.GetFiles(_pendingInvoicesPath, "invoice_*.json");
                
                foreach (var file in files)
                {
                    try
                    {
                        var json = File.ReadAllText(file);
                        var invoice = JsonConvert.DeserializeObject<Invoice>(json);
                        
                        if (invoice != null)
                        {
                            _pendingInvoices.Enqueue(invoice);
                        }
                    }
                    catch
                    {
                        // تجاهل الملفات التالفة
                    }
                }
            }
            catch
            {
                // تجاهل أخطاء التحميل
            }
        }

        private async Task TrySyncAsync()
        {
            try
            {
                if (await IsOnlineAsync())
                {
                    await SyncOfflineDataAsync();
                }
            }
            catch
            {
                // تجاهل أخطاء المزامنة التلقائية
            }
        }

        private async Task LogErrorAsync(string message)
        {
            try
            {
                var logPath = Path.Combine(_offlineDataPath, "errors.log");
                var logEntry = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} - ERROR: {message}{Environment.NewLine}";
                await File.AppendAllTextAsync(logPath, logEntry);
            }
            catch
            {
                // تجاهل أخطاء التسجيل
            }
        }

        private async Task LogInfoAsync(string message)
        {
            try
            {
                var logPath = Path.Combine(_offlineDataPath, "sync.log");
                var logEntry = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} - INFO: {message}{Environment.NewLine}";
                await File.AppendAllTextAsync(logPath, logEntry);
            }
            catch
            {
                // تجاهل أخطاء التسجيل
            }
        }

        public void Dispose()
        {
            _syncTimer?.Dispose();
        }
    }
}
