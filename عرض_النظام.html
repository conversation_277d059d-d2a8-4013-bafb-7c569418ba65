<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام نقطة البيع - عرض تفاعلي</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: #3498db;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            padding: 40px;
        }
        
        .feature-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            transition: transform 0.3s ease;
            border: 2px solid transparent;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            border-color: #3498db;
        }
        
        .feature-icon {
            font-size: 3em;
            margin-bottom: 20px;
        }
        
        .feature-title {
            font-size: 1.5em;
            color: #2c3e50;
            margin-bottom: 15px;
            font-weight: bold;
        }
        
        .feature-desc {
            color: #7f8c8d;
            line-height: 1.6;
        }
        
        .demo-section {
            background: #ecf0f1;
            padding: 40px;
        }
        
        .demo-title {
            text-align: center;
            font-size: 2em;
            color: #2c3e50;
            margin-bottom: 30px;
        }
        
        .pos-interface {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        
        .pos-header {
            background: #3498db;
            color: white;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .pos-content {
            display: grid;
            grid-template-columns: 1fr 2fr 1fr;
            gap: 20px;
            min-height: 400px;
        }
        
        .products-panel, .invoice-panel, .summary-panel {
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            padding: 15px;
        }
        
        .panel-title {
            background: #34495e;
            color: white;
            padding: 10px;
            margin: -15px -15px 15px -15px;
            border-radius: 6px 6px 0 0;
            text-align: center;
            font-weight: bold;
        }
        
        .product-btn {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 15px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            width: 100%;
            transition: background 0.3s;
        }
        
        .product-btn:hover {
            background: #c0392b;
        }
        
        .invoice-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .invoice-table th,
        .invoice-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        
        .invoice-table th {
            background: #f2f2f2;
        }
        
        .summary-item {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .total {
            font-weight: bold;
            font-size: 1.2em;
            color: #27ae60;
        }
        
        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
            justify-content: center;
        }
        
        .action-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            color: white;
            cursor: pointer;
            font-weight: bold;
            transition: opacity 0.3s;
        }
        
        .action-btn:hover {
            opacity: 0.8;
        }
        
        .btn-save { background: #27ae60; }
        .btn-print { background: #3498db; }
        .btn-sync { background: #f39c12; }
        .btn-new { background: #9b59b6; }
        
        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 5px;
            padding: 5px 10px;
            border-radius: 15px;
            background: rgba(255,255,255,0.2);
            font-size: 0.9em;
        }
        
        .download-section {
            background: #2c3e50;
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .download-btn {
            background: #e74c3c;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.2em;
            cursor: pointer;
            margin: 10px;
            transition: background 0.3s;
        }
        
        .download-btn:hover {
            background: #c0392b;
        }
        
        .requirements {
            background: #f8f9fa;
            padding: 30px;
            margin: 20px 0;
            border-radius: 10px;
            border-right: 5px solid #3498db;
        }
        
        @media (max-width: 768px) {
            .pos-content {
                grid-template-columns: 1fr;
            }
            
            .features {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏪 نظام نقطة البيع الاحترافي</h1>
            <p>الإصدار 1.1 - مع الضرائب المتغيرة والعمل بدون اتصال</p>
        </div>
        
        <div class="features">
            <div class="feature-card">
                <div class="feature-icon">💰</div>
                <div class="feature-title">ضرائب متغيرة</div>
                <div class="feature-desc">
                    نسب ضريبة مختلفة لكل منتج (0%, 5%, 10%, 15%)
                    مع دعم الضرائب الشاملة وغير الشاملة
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🔄</div>
                <div class="feature-title">عمل بدون اتصال</div>
                <div class="feature-desc">
                    حفظ تلقائي للفواتير عند انقطاع الاتصال
                    ومزامنة ذكية عند عودة الاتصال
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">📱</div>
                <div class="feature-title">واجهة عربية</div>
                <div class="feature-desc">
                    تصميم عصري مع دعم كامل للغة العربية
                    وواجهة بديهية سهلة الاستخدام
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🖨️</div>
                <div class="feature-title">طباعة احترافية</div>
                <div class="feature-desc">
                    طباعة فواتير منسقة باللغة العربية
                    مع دعم الطابعات الحرارية
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🔐</div>
                <div class="feature-title">نظام صلاحيات</div>
                <div class="feature-desc">
                    أدوار مختلفة للمستخدمين مع صلاحيات
                    مخصصة لكل عملية
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">📊</div>
                <div class="feature-title">تقارير مفصلة</div>
                <div class="feature-desc">
                    سجلات شاملة للمبيعات والمزامنة
                    مع إحصائيات مفصلة
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2 class="demo-title">🖥️ عرض الواجهة الرئيسية</h2>
            
            <div class="pos-interface">
                <div class="pos-header">
                    <div>
                        <strong>رقم الفاتورة:</strong> INV20241217001<br>
                        <small>2024/12/17 14:30</small>
                    </div>
                    <div>
                        <strong>المستخدم:</strong> مدير النظام
                    </div>
                    <div class="status-indicator">
                        ✅ متصل
                    </div>
                </div>
                
                <div class="pos-content">
                    <div class="products-panel">
                        <div class="panel-title">قائمة المنتجات</div>
                        <button class="product-btn" onclick="addProduct('خبز أبيض', 2.50, 0.15, false)">
                            خبز أبيض<br>2.50 ر.س
                        </button>
                        <button class="product-btn" onclick="addProduct('حليب طازج', 8.00, 0.05, true)">
                            حليب طازج<br>8.00 ر.س
                        </button>
                        <button class="product-btn" onclick="addProduct('سكر أبيض', 12.00, 0.00, false)">
                            سكر أبيض (معفى)<br>12.00 ر.س
                        </button>
                    </div>
                    
                    <div class="invoice-panel">
                        <div class="panel-title">عناصر الفاتورة</div>
                        <table class="invoice-table" id="invoiceTable">
                            <thead>
                                <tr>
                                    <th>المنتج</th>
                                    <th>الكمية</th>
                                    <th>السعر</th>
                                    <th>الضريبة</th>
                                    <th>المجموع</th>
                                </tr>
                            </thead>
                            <tbody id="invoiceItems">
                                <tr>
                                    <td colspan="5" style="color: #999; padding: 40px;">
                                        اضغط على المنتجات لإضافتها للفاتورة
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="summary-panel">
                        <div class="panel-title">ملخص الفاتورة</div>
                        <div class="summary-item">
                            <span>المجموع الفرعي:</span>
                            <span id="subtotal">0.00 ر.س</span>
                        </div>
                        <div class="summary-item">
                            <span>الضريبة:</span>
                            <span id="tax">0.00 ر.س</span>
                        </div>
                        <div class="summary-item total">
                            <span>الإجمالي:</span>
                            <span id="total">0.00 ر.س</span>
                        </div>
                    </div>
                </div>
                
                <div class="action-buttons">
                    <button class="action-btn btn-save">💾 حفظ الفاتورة</button>
                    <button class="action-btn btn-print">🖨️ طباعة</button>
                    <button class="action-btn btn-sync">🔄 مزامنة</button>
                    <button class="action-btn btn-new">📄 فاتورة جديدة</button>
                </div>
            </div>
        </div>
        
        <div class="requirements">
            <h3>📋 متطلبات التشغيل</h3>
            <ul style="margin: 15px 0; padding-right: 20px;">
                <li><strong>.NET 6.0 Runtime أو أحدث</strong> - مطلوب لتشغيل التطبيق</li>
                <li><strong>Windows 7 SP1 أو أحدث</strong> - نظام التشغيل المدعوم</li>
                <li><strong>2 GB RAM</strong> - الحد الأدنى للذاكرة</li>
                <li><strong>100 MB مساحة تخزين</strong> - للتطبيق والبيانات</li>
                <li><strong>دقة 1024×768 أو أعلى</strong> - لعرض الواجهة بشكل مثالي</li>
            </ul>
        </div>
        
        <div class="download-section">
            <h2>🚀 جاهز للتشغيل؟</h2>
            <p style="margin: 20px 0; font-size: 1.1em;">
                لتشغيل النظام الفعلي، تحتاج لتثبيت .NET 6.0 أولاً
            </p>
            
            <a href="https://dotnet.microsoft.com/download/dotnet/6.0" target="_blank">
                <button class="download-btn">📥 تحميل .NET 6.0</button>
            </a>
            
            <p style="margin: 20px 0; opacity: 0.8;">
                بعد التثبيت، قم بتشغيل ملف "تشغيل_النظام.bat"
            </p>
        </div>
    </div>
    
    <script>
        let invoiceItems = [];
        let itemCounter = 0;
        
        function addProduct(name, price, taxRate, isTaxInclusive) {
            itemCounter++;
            
            let basePrice = price;
            let taxAmount = 0;
            let total = price;
            
            if (taxRate > 0) {
                if (isTaxInclusive) {
                    // ضريبة شاملة: استخراج السعر الأساسي
                    basePrice = price / (1 + taxRate);
                    taxAmount = price - basePrice;
                } else {
                    // ضريبة غير شاملة: إضافة الضريبة
                    taxAmount = price * taxRate;
                    total = price + taxAmount;
                }
            }
            
            const item = {
                id: itemCounter,
                name: name,
                quantity: 1,
                basePrice: basePrice,
                taxRate: taxRate,
                taxAmount: taxAmount,
                total: total,
                isTaxInclusive: isTaxInclusive
            };
            
            invoiceItems.push(item);
            updateInvoiceDisplay();
        }
        
        function updateInvoiceDisplay() {
            const tbody = document.getElementById('invoiceItems');
            
            if (invoiceItems.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="5" style="color: #999; padding: 40px;">
                            اضغط على المنتجات لإضافتها للفاتورة
                        </td>
                    </tr>
                `;
                updateSummary();
                return;
            }
            
            tbody.innerHTML = invoiceItems.map(item => `
                <tr>
                    <td>${item.name}</td>
                    <td>${item.quantity}</td>
                    <td>${item.basePrice.toFixed(2)} ر.س</td>
                    <td>${item.taxAmount.toFixed(2)} ر.س (${(item.taxRate * 100).toFixed(0)}%)</td>
                    <td>${item.total.toFixed(2)} ر.س</td>
                </tr>
            `).join('');
            
            updateSummary();
        }
        
        function updateSummary() {
            const subtotal = invoiceItems.reduce((sum, item) => sum + item.basePrice * item.quantity, 0);
            const tax = invoiceItems.reduce((sum, item) => sum + item.taxAmount * item.quantity, 0);
            const total = subtotal + tax;
            
            document.getElementById('subtotal').textContent = subtotal.toFixed(2) + ' ر.س';
            document.getElementById('tax').textContent = tax.toFixed(2) + ' ر.س';
            document.getElementById('total').textContent = total.toFixed(2) + ' ر.س';
        }
        
        // محاكاة تغيير حالة الاتصال
        function simulateConnectionStatus() {
            const statusIndicator = document.querySelector('.status-indicator');
            const statuses = [
                { text: '✅ متصل', color: '#27ae60' },
                { text: '🔄 مزامنة (2 معلق)', color: '#f39c12' },
                { text: '❌ غير متصل (3 معلق)', color: '#e74c3c' }
            ];
            
            let currentIndex = 0;
            setInterval(() => {
                currentIndex = (currentIndex + 1) % statuses.length;
                const status = statuses[currentIndex];
                statusIndicator.innerHTML = status.text;
                statusIndicator.style.background = status.color + '40';
            }, 3000);
        }
        
        // بدء محاكاة حالة الاتصال
        setTimeout(simulateConnectionStatus, 2000);
        
        // إضافة تأثيرات للأزرار
        document.querySelectorAll('.action-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
                
                if (this.textContent.includes('فاتورة جديدة')) {
                    invoiceItems = [];
                    itemCounter = 0;
                    updateInvoiceDisplay();
                }
            });
        });
    </script>
</body>
</html>
