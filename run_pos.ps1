# POS System Runner
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "        POS System - نظام نقطة البيع" -ForegroundColor Cyan
Write-Host "              Version 1.1" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "Checking .NET installation..." -ForegroundColor Yellow

# Find .NET executable
$dotnetPath = ""
if (Test-Path "C:\Program Files\dotnet\dotnet.exe") {
    $dotnetPath = "C:\Program Files\dotnet\dotnet.exe"
} elseif (Test-Path "C:\Program Files (x86)\dotnet\dotnet.exe") {
    $dotnetPath = "C:\Program Files (x86)\dotnet\dotnet.exe"
} else {
    Write-Host "❌ .NET not found!" -ForegroundColor Red
    Write-Host "Please install .NET 6.0 SDK from:" -ForegroundColor Red
    Write-Host "https://dotnet.microsoft.com/download/dotnet/6.0" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "✅ .NET found at: $dotnetPath" -ForegroundColor Green

# Get version
$version = & $dotnetPath --version
Write-Host "Version: $version" -ForegroundColor Green
Write-Host ""

Write-Host "Building project..." -ForegroundColor Yellow
$buildResult = & $dotnetPath build POSSystem.csproj --configuration Release 2>&1

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Build failed!" -ForegroundColor Red
    Write-Host "Build output:" -ForegroundColor Red
    Write-Host $buildResult -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "✅ Build successful!" -ForegroundColor Green
Write-Host ""

Write-Host "🚀 Starting POS System..." -ForegroundColor Cyan
Write-Host ""
Write-Host "🔐 Login credentials:" -ForegroundColor Yellow
Write-Host "   Username: admin" -ForegroundColor White
Write-Host "   Password: any password" -ForegroundColor White
Write-Host ""

# Run the application
& $dotnetPath run --project POSSystem.csproj --configuration Release

Write-Host ""
Write-Host "System closed." -ForegroundColor Yellow
Read-Host "Press Enter to exit"
