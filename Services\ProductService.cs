using POSSystem.Models;

namespace POSSystem.Services
{
    /// <summary>
    /// خدمة المنتجات
    /// </summary>
    public class ProductService : IProductService
    {
        private readonly IApiService _apiService;
        private List<Product>? _cachedProducts;
        private List<Category>? _cachedCategories;
        private DateTime _lastCacheUpdate = DateTime.MinValue;
        private readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(5);

        public ProductService(IApiService apiService)
        {
            _apiService = apiService;
        }

        public async Task<List<Product>> SearchProductsAsync(ProductSearchRequest request)
        {
            try
            {
                // محاولة الحصول على البيانات من API
                var products = await _apiService.PostAsync<List<Product>>("products/search", request);
                
                if (products != null)
                {
                    return products;
                }
                
                // في حالة فشل API، استخدام البيانات المحفوظة محلياً
                return await GetCachedProductsAsync(request);
            }
            catch (Exception)
            {
                return await GetCachedProductsAsync(request);
            }
        }

        public async Task<Product?> GetProductByCodeAsync(string code)
        {
            try
            {
                var product = await _apiService.GetAsync<Product>($"products/code/{code}");
                
                if (product != null)
                {
                    return product;
                }
                
                // البحث في البيانات المحفوظة محلياً
                var cachedProducts = await GetAllProductsAsync();
                return cachedProducts.FirstOrDefault(p => p.Code.Equals(code, StringComparison.OrdinalIgnoreCase));
            }
            catch (Exception)
            {
                var cachedProducts = await GetAllProductsAsync();
                return cachedProducts.FirstOrDefault(p => p.Code.Equals(code, StringComparison.OrdinalIgnoreCase));
            }
        }

        public async Task<Product?> GetProductByBarcodeAsync(string barcode)
        {
            try
            {
                var product = await _apiService.GetAsync<Product>($"products/barcode/{barcode}");
                
                if (product != null)
                {
                    return product;
                }
                
                // البحث في البيانات المحفوظة محلياً
                var cachedProducts = await GetAllProductsAsync();
                return cachedProducts.FirstOrDefault(p => p.Barcode == barcode);
            }
            catch (Exception)
            {
                var cachedProducts = await GetAllProductsAsync();
                return cachedProducts.FirstOrDefault(p => p.Barcode == barcode);
            }
        }

        public async Task<List<Category>> GetCategoriesAsync()
        {
            try
            {
                // التحقق من صحة البيانات المحفوظة
                if (_cachedCategories != null && DateTime.Now - _lastCacheUpdate < _cacheExpiry)
                {
                    return _cachedCategories;
                }

                var categories = await _apiService.GetAsync<List<Category>>("categories");
                
                if (categories != null)
                {
                    _cachedCategories = categories;
                    _lastCacheUpdate = DateTime.Now;
                    SaveCategoriesLocally(categories);
                    return categories;
                }
                
                // استخدام البيانات المحفوظة محلياً
                return LoadCategoriesLocally();
            }
            catch (Exception)
            {
                return LoadCategoriesLocally();
            }
        }

        public async Task<List<Product>> GetProductsByCategoryAsync(int categoryId)
        {
            var request = new ProductSearchRequest
            {
                CategoryId = categoryId,
                IsActive = true,
                PageSize = 100
            };
            
            return await SearchProductsAsync(request);
        }

        public async Task<bool> UpdateProductStockAsync(int productId, decimal quantity)
        {
            try
            {
                var updateData = new { ProductId = productId, Quantity = quantity };
                var result = await _apiService.PostAsync<object>("products/update-stock", updateData);
                
                return result != null;
            }
            catch (Exception)
            {
                return false;
            }
        }

        private async Task<List<Product>> GetAllProductsAsync()
        {
            // التحقق من صحة البيانات المحفوظة
            if (_cachedProducts != null && DateTime.Now - _lastCacheUpdate < _cacheExpiry)
            {
                return _cachedProducts;
            }

            try
            {
                var products = await _apiService.GetAsync<List<Product>>("products");
                
                if (products != null)
                {
                    _cachedProducts = products;
                    _lastCacheUpdate = DateTime.Now;
                    SaveProductsLocally(products);
                    return products;
                }
            }
            catch (Exception)
            {
                // تجاهل الخطأ والمتابعة للبيانات المحفوظة محلياً
            }
            
            return LoadProductsLocally();
        }

        private async Task<List<Product>> GetCachedProductsAsync(ProductSearchRequest request)
        {
            var allProducts = await GetAllProductsAsync();
            
            var query = allProducts.AsQueryable();
            
            if (!string.IsNullOrEmpty(request.SearchTerm))
            {
                query = query.Where(p => p.Name.Contains(request.SearchTerm, StringComparison.OrdinalIgnoreCase) ||
                                        p.Code.Contains(request.SearchTerm, StringComparison.OrdinalIgnoreCase));
            }
            
            if (!string.IsNullOrEmpty(request.Code))
            {
                query = query.Where(p => p.Code.Equals(request.Code, StringComparison.OrdinalIgnoreCase));
            }
            
            if (!string.IsNullOrEmpty(request.Barcode))
            {
                query = query.Where(p => p.Barcode == request.Barcode);
            }
            
            if (request.CategoryId.HasValue)
            {
                query = query.Where(p => p.CategoryId == request.CategoryId.Value);
            }
            
            if (request.IsActive.HasValue)
            {
                query = query.Where(p => p.IsActive == request.IsActive.Value);
            }
            
            return query.Skip((request.Page - 1) * request.PageSize)
                       .Take(request.PageSize)
                       .ToList();
        }

        private void SaveProductsLocally(List<Product> products)
        {
            try
            {
                var json = Newtonsoft.Json.JsonConvert.SerializeObject(products);
                var filePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "POSSystem", "products.json");
                
                Directory.CreateDirectory(Path.GetDirectoryName(filePath)!);
                File.WriteAllText(filePath, json);
            }
            catch (Exception)
            {
                // تجاهل أخطاء الحفظ
            }
        }

        private List<Product> LoadProductsLocally()
        {
            try
            {
                var filePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "POSSystem", "products.json");
                
                if (File.Exists(filePath))
                {
                    var json = File.ReadAllText(filePath);
                    var products = Newtonsoft.Json.JsonConvert.DeserializeObject<List<Product>>(json);
                    return products ?? new List<Product>();
                }
            }
            catch (Exception)
            {
                // تجاهل أخطاء القراءة
            }
            
            // إرجاع بيانات افتراضية
            return GetDefaultProducts();
        }

        private void SaveCategoriesLocally(List<Category> categories)
        {
            try
            {
                var json = Newtonsoft.Json.JsonConvert.SerializeObject(categories);
                var filePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "POSSystem", "categories.json");
                
                Directory.CreateDirectory(Path.GetDirectoryName(filePath)!);
                File.WriteAllText(filePath, json);
            }
            catch (Exception)
            {
                // تجاهل أخطاء الحفظ
            }
        }

        private List<Category> LoadCategoriesLocally()
        {
            try
            {
                var filePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "POSSystem", "categories.json");
                
                if (File.Exists(filePath))
                {
                    var json = File.ReadAllText(filePath);
                    var categories = Newtonsoft.Json.JsonConvert.DeserializeObject<List<Category>>(json);
                    return categories ?? new List<Category>();
                }
            }
            catch (Exception)
            {
                // تجاهل أخطاء القراءة
            }
            
            // إرجاع بيانات افتراضية
            return GetDefaultCategories();
        }

        private List<Product> GetDefaultProducts()
        {
            return new List<Product>
            {
                new Product { Id = 1, Code = "001", Name = "خبز أبيض", Price = 2.50m, Barcode = "1234567890123", CategoryId = 1, IsActive = true },
                new Product { Id = 2, Code = "002", Name = "حليب طازج", Price = 8.00m, Barcode = "1234567890124", CategoryId = 2, IsActive = true },
                new Product { Id = 3, Code = "003", Name = "أرز بسمتي", Price = 25.00m, Barcode = "1234567890125", CategoryId = 1, IsActive = true },
                new Product { Id = 4, Code = "004", Name = "زيت زيتون", Price = 45.00m, Barcode = "1234567890126", CategoryId = 3, IsActive = true },
                new Product { Id = 5, Code = "005", Name = "سكر أبيض", Price = 12.00m, Barcode = "1234567890127", CategoryId = 1, IsActive = true },
                new Product { Id = 6, Code = "006", Name = "شاي أحمر", Price = 15.00m, Barcode = "1234567890128", CategoryId = 4, IsActive = true },
                new Product { Id = 7, Code = "007", Name = "قهوة عربية", Price = 35.00m, Barcode = "1234567890129", CategoryId = 4, IsActive = true },
                new Product { Id = 8, Code = "008", Name = "جبن أبيض", Price = 18.00m, Barcode = "1234567890130", CategoryId = 2, IsActive = true }
            };
        }

        private List<Category> GetDefaultCategories()
        {
            return new List<Category>
            {
                new Category { Id = 1, Name = "مواد غذائية أساسية", Color = "#FF6B6B", IsActive = true },
                new Category { Id = 2, Name = "منتجات الألبان", Color = "#4ECDC4", IsActive = true },
                new Category { Id = 3, Name = "زيوت ومواد طبخ", Color = "#45B7D1", IsActive = true },
                new Category { Id = 4, Name = "مشروبات", Color = "#96CEB4", IsActive = true },
                new Category { Id = 5, Name = "حلويات ومكسرات", Color = "#FFEAA7", IsActive = true }
            };
        }
    }
}
