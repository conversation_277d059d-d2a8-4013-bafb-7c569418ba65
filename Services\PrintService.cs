using POSSystem.Models;
using System.Drawing;
using System.Drawing.Printing;
using System.Text;
using Microsoft.Extensions.Configuration;

namespace POSSystem.Services
{
    /// <summary>
    /// خدمة الطباعة
    /// </summary>
    public class PrintService : IPrintService
    {
        private readonly IConfiguration _configuration;
        private PrintDocument _printDocument;
        private Invoice? _currentInvoice;
        private string _printContent = string.Empty;

        public PrintService(IConfiguration configuration)
        {
            _configuration = configuration;
            _printDocument = new PrintDocument();
            _printDocument.PrintPage += PrintDocument_PrintPage;
        }

        public async Task<bool> PrintInvoiceAsync(Invoice invoice)
        {
            try
            {
                _currentInvoice = invoice;
                
                // إعداد الطابعة
                var defaultPrinter = _configuration["PrinterSettings:DefaultPrinter"];
                if (!string.IsNullOrEmpty(defaultPrinter))
                {
                    _printDocument.PrinterSettings.PrinterName = defaultPrinter;
                }

                // إعداد حجم الورق (80mm للطابعات الحرارية)
                var paperWidth = _configuration.GetValue<int>("PrinterSettings:PaperWidth", 80);
                _printDocument.DefaultPageSettings.PaperSize = new PaperSize("Custom", paperWidth * 4, 1000); // 80mm = 320 units

                // طباعة الفاتورة
                _printDocument.Print();
                
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> PrintReportAsync(string reportContent)
        {
            try
            {
                _printContent = reportContent;
                _currentInvoice = null;
                
                _printDocument.Print();
                
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public List<string> GetAvailablePrinters()
        {
            var printers = new List<string>();
            
            foreach (string printerName in PrinterSettings.InstalledPrinters)
            {
                printers.Add(printerName);
            }
            
            return printers;
        }

        public void SetDefaultPrinter(string printerName)
        {
            _printDocument.PrinterSettings.PrinterName = printerName;
        }

        private void PrintDocument_PrintPage(object sender, PrintPageEventArgs e)
        {
            if (_currentInvoice != null)
            {
                PrintInvoiceContent(e);
            }
            else if (!string.IsNullOrEmpty(_printContent))
            {
                PrintReportContent(e);
            }
        }

        private void PrintInvoiceContent(PrintPageEventArgs e)
        {
            if (_currentInvoice == null || e.Graphics == null) return;

            var graphics = e.Graphics;
            var fontSize = _configuration.GetValue<int>("PrinterSettings:FontSize", 10);
            var font = new Font("Arial", fontSize);
            var boldFont = new Font("Arial", fontSize, FontStyle.Bold);
            var titleFont = new Font("Arial", fontSize + 2, FontStyle.Bold);
            
            var brush = Brushes.Black;
            var x = 10;
            var y = 10;
            var lineHeight = fontSize + 5;

            // عنوان المتجر
            var storeTitle = "نظام نقطة البيع";
            graphics.DrawString(storeTitle, titleFont, brush, x, y);
            y += lineHeight * 2;

            // معلومات الفاتورة
            graphics.DrawString($"رقم الفاتورة: {_currentInvoice.InvoiceNumber}", boldFont, brush, x, y);
            y += lineHeight;
            
            graphics.DrawString($"التاريخ: {_currentInvoice.InvoiceDate:yyyy/MM/dd HH:mm}", font, brush, x, y);
            y += lineHeight;
            
            graphics.DrawString($"الكاشير: {_currentInvoice.User?.FullName ?? "غير محدد"}", font, brush, x, y);
            y += lineHeight * 2;

            // خط فاصل
            graphics.DrawString(new string('-', 40), font, brush, x, y);
            y += lineHeight;

            // عناوين الأعمدة
            graphics.DrawString("الصنف", boldFont, brush, x, y);
            graphics.DrawString("الكمية", boldFont, brush, x + 150, y);
            graphics.DrawString("السعر", boldFont, brush, x + 200, y);
            graphics.DrawString("المجموع", boldFont, brush, x + 250, y);
            y += lineHeight;

            graphics.DrawString(new string('-', 40), font, brush, x, y);
            y += lineHeight;

            // عناصر الفاتورة
            foreach (var item in _currentInvoice.Items)
            {
                graphics.DrawString(item.ProductName, font, brush, x, y);
                graphics.DrawString(item.Quantity.ToString("F2"), font, brush, x + 150, y);
                graphics.DrawString(item.UnitPrice.ToString("F2"), font, brush, x + 200, y);
                graphics.DrawString(item.Total.ToString("F2"), font, brush, x + 250, y);
                y += lineHeight;
            }

            // خط فاصل
            y += lineHeight;
            graphics.DrawString(new string('-', 40), font, brush, x, y);
            y += lineHeight;

            // الإجماليات
            graphics.DrawString($"المجموع الفرعي: {_currentInvoice.SubTotal:F2} ر.س", font, brush, x, y);
            y += lineHeight;

            if (_currentInvoice.DiscountAmount > 0)
            {
                graphics.DrawString($"الخصم: {_currentInvoice.DiscountAmount:F2} ر.س", font, brush, x, y);
                y += lineHeight;
            }

            graphics.DrawString($"الضريبة: {_currentInvoice.TaxAmount:F2} ر.س", font, brush, x, y);
            y += lineHeight;

            graphics.DrawString($"الإجمالي: {_currentInvoice.Total:F2} ر.س", boldFont, brush, x, y);
            y += lineHeight;

            graphics.DrawString($"المدفوع: {_currentInvoice.PaidAmount:F2} ر.س", font, brush, x, y);
            y += lineHeight;

            if (_currentInvoice.ChangeAmount > 0)
            {
                graphics.DrawString($"الباقي: {_currentInvoice.ChangeAmount:F2} ر.س", font, brush, x, y);
                y += lineHeight;
            }

            // رسالة شكر
            y += lineHeight * 2;
            graphics.DrawString("شكراً لزيارتكم", titleFont, brush, x + 50, y);
            y += lineHeight;
            graphics.DrawString($"تاريخ الطباعة: {DateTime.Now:yyyy/MM/dd HH:mm}", font, brush, x, y);
        }

        private void PrintReportContent(PrintPageEventArgs e)
        {
            if (e.Graphics == null) return;

            var graphics = e.Graphics;
            var fontSize = _configuration.GetValue<int>("PrinterSettings:FontSize", 10);
            var font = new Font("Arial", fontSize);
            var brush = Brushes.Black;
            var x = 10;
            var y = 10;
            var lineHeight = fontSize + 5;

            var lines = _printContent.Split('\n');
            
            foreach (var line in lines)
            {
                graphics.DrawString(line, font, brush, x, y);
                y += lineHeight;
                
                // التحقق من عدم تجاوز حدود الصفحة
                if (y > e.MarginBounds.Bottom)
                {
                    e.HasMorePages = true;
                    return;
                }
            }
        }
    }
}
