using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using System.Text;
using System.Net.Http.Headers;
using POSSystem.Models;

namespace POSSystem.Services
{
    /// <summary>
    /// خدمة API الأساسية
    /// </summary>
    public class ApiService : IApiService
    {
        private readonly HttpClient _httpClient;
        private readonly IConfiguration _configuration;
        private readonly string _baseUrl;
        private readonly string _apiKey;

        public ApiService(HttpClient httpClient, IConfiguration configuration)
        {
            _httpClient = httpClient;
            _configuration = configuration;
            _baseUrl = _configuration["ApiSettings:BaseUrl"] ?? "https://api.yourpos.com";
            _apiKey = _configuration["ApiSettings:ApiKey"] ?? "";

            // إعداد HttpClient
            _httpClient.BaseAddress = new Uri(_baseUrl);
            _httpClient.DefaultRequestHeaders.Accept.Clear();
            _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            
            if (!string.IsNullOrEmpty(_apiKey))
            {
                _httpClient.DefaultRequestHeaders.Add("X-API-Key", _apiKey);
            }

            var timeout = _configuration.GetValue<int>("ApiSettings:Timeout", 30);
            _httpClient.Timeout = TimeSpan.FromSeconds(timeout);
        }

        public async Task<T?> GetAsync<T>(string endpoint)
        {
            try
            {
                var response = await _httpClient.GetAsync(endpoint);
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    return JsonConvert.DeserializeObject<T>(content);
                }
                
                // في حالة عدم توفر الاتصال، إرجاع بيانات تجريبية
                if (!response.IsSuccessStatusCode)
                {
                    return GetMockData<T>(endpoint);
                }
                
                return default(T);
            }
            catch (HttpRequestException)
            {
                // في حالة عدم توفر الاتصال، إرجاع بيانات تجريبية
                return GetMockData<T>(endpoint);
            }
            catch (TaskCanceledException)
            {
                // انتهت مهلة الطلب
                return GetMockData<T>(endpoint);
            }
        }

        public async Task<T?> PostAsync<T>(string endpoint, object data)
        {
            try
            {
                var json = JsonConvert.SerializeObject(data);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                var response = await _httpClient.PostAsync(endpoint, content);
                
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    return JsonConvert.DeserializeObject<T>(responseContent);
                }
                
                return default(T);
            }
            catch (Exception)
            {
                return default(T);
            }
        }

        public async Task<T?> PutAsync<T>(string endpoint, object data)
        {
            try
            {
                var json = JsonConvert.SerializeObject(data);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                var response = await _httpClient.PutAsync(endpoint, content);
                
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    return JsonConvert.DeserializeObject<T>(responseContent);
                }
                
                return default(T);
            }
            catch (Exception)
            {
                return default(T);
            }
        }

        public async Task<bool> DeleteAsync(string endpoint)
        {
            try
            {
                var response = await _httpClient.DeleteAsync(endpoint);
                return response.IsSuccessStatusCode;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public void SetAuthToken(string token)
        {
            _httpClient.DefaultRequestHeaders.Authorization = 
                new AuthenticationHeaderValue("Bearer", token);
        }

        public void ClearAuthToken()
        {
            _httpClient.DefaultRequestHeaders.Authorization = null;
        }

        /// <summary>
        /// إرجاع بيانات تجريبية في حالة عدم توفر الاتصال
        /// </summary>
        private T? GetMockData<T>(string endpoint)
        {
            // هذه دالة لإرجاع بيانات تجريبية عندما لا يكون هناك اتصال بالإنترنت
            // يمكن تخصيصها حسب نوع البيانات المطلوبة
            
            if (typeof(T) == typeof(List<Models.Product>))
            {
                return (T)(object)GetMockProducts();
            }
            
            if (typeof(T) == typeof(List<Models.Category>))
            {
                return (T)(object)GetMockCategories();
            }
            
            if (typeof(T) == typeof(Models.LoginResponse))
            {
                return (T)(object)GetMockLoginResponse();
            }
            
            return default(T);
        }

        private List<Models.Product> GetMockProducts()
        {
            return new List<Models.Product>
            {
                new Models.Product { Id = 1, Code = "001", Name = "خبز أبيض", Price = 2.50m, Barcode = "1234567890123", CategoryId = 1 },
                new Models.Product { Id = 2, Code = "002", Name = "حليب طازج", Price = 8.00m, Barcode = "1234567890124", CategoryId = 2 },
                new Models.Product { Id = 3, Code = "003", Name = "أرز بسمتي", Price = 25.00m, Barcode = "1234567890125", CategoryId = 1 },
                new Models.Product { Id = 4, Code = "004", Name = "زيت زيتون", Price = 45.00m, Barcode = "1234567890126", CategoryId = 3 },
                new Models.Product { Id = 5, Code = "005", Name = "سكر أبيض", Price = 12.00m, Barcode = "1234567890127", CategoryId = 1 }
            };
        }

        private List<Models.Category> GetMockCategories()
        {
            return new List<Models.Category>
            {
                new Models.Category { Id = 1, Name = "مواد غذائية أساسية", Color = "#FF6B6B" },
                new Models.Category { Id = 2, Name = "منتجات الألبان", Color = "#4ECDC4" },
                new Models.Category { Id = 3, Name = "زيوت ومواد طبخ", Color = "#45B7D1" },
                new Models.Category { Id = 4, Name = "مشروبات", Color = "#96CEB4" },
                new Models.Category { Id = 5, Name = "حلويات ومكسرات", Color = "#FFEAA7" }
            };
        }

        private Models.LoginResponse GetMockLoginResponse()
        {
            return new Models.LoginResponse
            {
                Success = true,
                Message = "تم تسجيل الدخول بنجاح",
                User = new Models.User
                {
                    Id = 1,
                    Username = "admin",
                    FullName = "مدير النظام",
                    Email = "<EMAIL>",
                    Role = Models.UserRole.Admin,
                    Permissions = new Models.UserPermissions
                    {
                        CanEditPrices = true,
                        CanApplyDiscounts = true,
                        CanDeleteItems = true,
                        CanVoidInvoices = true,
                        CanAccessReports = true,
                        CanManageUsers = true,
                        CanManageProducts = true,
                        CanCloseCash = true,
                        CanOpenCash = true,
                        CanPrintReports = true,
                        MaxDiscountPercentage = 50,
                        MaxDiscountAmount = 1000
                    }
                },
                Token = "mock-jwt-token",
                ExpiresAt = DateTime.Now.AddHours(8)
            };
        }
    }
}
