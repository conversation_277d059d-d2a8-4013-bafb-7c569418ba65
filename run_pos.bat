@echo off
chcp 65001 > nul
title POS System - نظام نقطة البيع

echo ========================================
echo        POS System - نظام نقطة البيع
echo              Version 1.1
echo ========================================
echo.

echo Checking .NET installation...
echo.

REM Find .NET installation
set DOTNET_EXE=""
if exist "C:\Program Files\dotnet\dotnet.exe" (
    set DOTNET_EXE="C:\Program Files\dotnet\dotnet.exe"
    goto :dotnet_found
)

if exist "C:\Program Files (x86)\dotnet\dotnet.exe" (
    set DOTNET_EXE="C:\Program Files (x86)\dotnet\dotnet.exe"
    goto :dotnet_found
)

REM Try system PATH
dotnet --version > nul 2>&1
if %errorlevel% equ 0 (
    set DOTNET_EXE=dotnet
    goto :dotnet_found
)

echo ❌ .NET not found!
echo Please install .NET 6.0 SDK from:
echo https://dotnet.microsoft.com/download/dotnet/6.0
pause
exit /b 1

:dotnet_found
echo ✅ .NET found and working!
echo Version:
%DOTNET_EXE% --version
echo.

echo Building project...
%DOTNET_EXE% build POSSystem.csproj --configuration Release > build.log 2>&1
if %errorlevel% neq 0 (
    echo ❌ Build failed!
    echo Build log:
    type build.log
    pause
    exit /b 1
)

echo ✅ Build successful!
echo.

echo 🚀 Starting POS System...
echo.
echo 🔐 Login credentials:
echo    Username: admin
echo    Password: any password
echo.

REM Run the application
%DOTNET_EXE% run --project POSSystem.csproj --configuration Release

echo.
echo System closed.
pause
