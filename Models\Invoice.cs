using System.ComponentModel.DataAnnotations;

namespace POSSystem.Models
{
    /// <summary>
    /// نموذج الفاتورة
    /// </summary>
    public class Invoice
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(20)]
        public string InvoiceNumber { get; set; } = string.Empty;
        
        public DateTime InvoiceDate { get; set; } = DateTime.Now;
        
        public int UserId { get; set; }
        
        public User? User { get; set; }
        
        public int? CustomerId { get; set; }
        
        public Customer? Customer { get; set; }
        
        public List<InvoiceItem> Items { get; set; } = new();
        
        public decimal SubTotal { get; set; }
        
        public decimal DiscountAmount { get; set; }
        
        public decimal DiscountPercentage { get; set; }
        
        public decimal TaxAmount { get; set; }
        
        public decimal Total { get; set; }
        
        public decimal PaidAmount { get; set; }
        
        public decimal ChangeAmount { get; set; }
        
        public PaymentMethod PaymentMethod { get; set; } = PaymentMethod.Cash;
        
        public InvoiceStatus Status { get; set; } = InvoiceStatus.Pending;
        
        public string? Notes { get; set; }
        
        public bool IsPrinted { get; set; } = false;
        
        public DateTime? PrintedAt { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        public DateTime? UpdatedAt { get; set; }
        
        /// <summary>
        /// حساب إجمالي الفاتورة
        /// </summary>
        public void CalculateTotal()
        {
            SubTotal = Items.Sum(x => x.Total);
            
            // حساب الخصم
            var discountValue = DiscountPercentage > 0 
                ? (SubTotal * DiscountPercentage / 100) 
                : DiscountAmount;
            
            var afterDiscount = SubTotal - discountValue;
            
            // حساب الضريبة
            TaxAmount = Items.Sum(x => x.TaxAmount);
            
            Total = afterDiscount + TaxAmount;
            
            // حساب الباقي
            ChangeAmount = Math.Max(0, PaidAmount - Total);
        }
        
        /// <summary>
        /// إضافة منتج للفاتورة
        /// </summary>
        public void AddItem(Product product, decimal quantity, decimal? customPrice = null)
        {
            var existingItem = Items.FirstOrDefault(x => x.ProductId == product.Id);
            
            if (existingItem != null)
            {
                existingItem.Quantity += quantity;
                existingItem.CalculateTotal();
            }
            else
            {
                var item = new InvoiceItem
                {
                    ProductId = product.Id,
                    Product = product,
                    ProductName = product.Name,
                    ProductCode = product.Code,
                    Quantity = quantity,
                    UnitPrice = customPrice ?? product.FinalPrice,
                    TaxRate = product.TaxRate,
                    IsTaxable = product.IsTaxable
                };
                item.CalculateTotal();
                Items.Add(item);
            }
            
            CalculateTotal();
        }
        
        /// <summary>
        /// حذف منتج من الفاتورة
        /// </summary>
        public void RemoveItem(int productId)
        {
            var item = Items.FirstOrDefault(x => x.ProductId == productId);
            if (item != null)
            {
                Items.Remove(item);
                CalculateTotal();
            }
        }
        
        /// <summary>
        /// تحديث كمية منتج
        /// </summary>
        public void UpdateItemQuantity(int productId, decimal newQuantity)
        {
            var item = Items.FirstOrDefault(x => x.ProductId == productId);
            if (item != null)
            {
                if (newQuantity <= 0)
                {
                    RemoveItem(productId);
                }
                else
                {
                    item.Quantity = newQuantity;
                    item.CalculateTotal();
                    CalculateTotal();
                }
            }
        }
    }

    /// <summary>
    /// عنصر الفاتورة
    /// </summary>
    public class InvoiceItem
    {
        public int Id { get; set; }
        
        public int InvoiceId { get; set; }
        
        public int ProductId { get; set; }
        
        public Product? Product { get; set; }
        
        [Required]
        [StringLength(100)]
        public string ProductName { get; set; } = string.Empty;
        
        [StringLength(20)]
        public string ProductCode { get; set; } = string.Empty;
        
        public decimal Quantity { get; set; }
        
        public decimal UnitPrice { get; set; }
        
        public decimal DiscountAmount { get; set; }
        
        public decimal DiscountPercentage { get; set; }
        
        public decimal TaxRate { get; set; }
        
        public bool IsTaxable { get; set; } = true;
        
        public decimal SubTotal { get; set; }
        
        public decimal TaxAmount { get; set; }
        
        public decimal Total { get; set; }
        
        /// <summary>
        /// حساب إجمالي العنصر
        /// </summary>
        public void CalculateTotal()
        {
            SubTotal = Quantity * UnitPrice;
            
            // حساب الخصم
            var discountValue = DiscountPercentage > 0 
                ? (SubTotal * DiscountPercentage / 100) 
                : DiscountAmount;
            
            var afterDiscount = SubTotal - discountValue;
            
            // حساب الضريبة
            TaxAmount = IsTaxable ? (afterDiscount * TaxRate) : 0;
            
            Total = afterDiscount + TaxAmount;
        }
    }

    /// <summary>
    /// طرق الدفع
    /// </summary>
    public enum PaymentMethod
    {
        Cash = 1,           // نقدي
        CreditCard = 2,     // بطاقة ائتمان
        DebitCard = 3,      // بطاقة خصم
        BankTransfer = 4,   // تحويل بنكي
        Check = 5,          // شيك
        Gift = 6,           // هدية
        Mixed = 7           // مختلط
    }

    /// <summary>
    /// حالة الفاتورة
    /// </summary>
    public enum InvoiceStatus
    {
        Pending = 1,    // معلقة
        Completed = 2,  // مكتملة
        Cancelled = 3,  // ملغية
        Returned = 4    // مرتجعة
    }

    /// <summary>
    /// العميل
    /// </summary>
    public class Customer
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(20)]
        public string? Phone { get; set; }
        
        [EmailAddress]
        public string? Email { get; set; }
        
        [StringLength(200)]
        public string? Address { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        public List<Invoice> Invoices { get; set; } = new();
    }
}
