# نظام نقطة البيع (POS System)

نظام نقطة بيع احترافي مصمم خصيصاً للسوبرماركت باللغة العربية، مطور بـ C# و Windows Forms.

## 🌟 المميزات الرئيسية

### 🔐 نظام المصادقة والصلاحيات
- تسجيل دخول آمن مع التحقق من الصلاحيات
- أدوار مختلفة للمستخدمين (مدير، كاشير، موظف)
- صلاحيات مخصصة لكل مستخدم (تعديل الأسعار، الخصومات، الحذف)
- جلسات آمنة مع انتهاء صلاحية تلقائي

### 🛒 إدارة المبيعات
- **ثلاث طرق لإدخال المنتجات:**
  - يدوياً بإدخال الكود
  - بواسطة قارئ الباركود
  - بالضغط على أزرار المنتجات
- جدول تفاعلي لعرض عناصر الفاتورة
- تعديل الكميات والأسعار (حسب الصلاحيات)
- تطبيق خصومات على مستوى العنصر أو الفاتورة

### 🧮 نظام الحسابات
- حساب تلقائي للإجماليات والضرائب
- دعم أنواع مختلفة من الخصومات
- حساب الباقي والمدفوع
- دعم طرق دفع متعددة (نقدي، بطاقة ائتمان، تحويل بنكي)

### 📦 إدارة المنتجات
- تصنيف المنتجات حسب الفئات
- عرض المنتجات بأزرار ملونة حسب الفئة
- بحث سريع بالكود أو الباركود
- تتبع المخزون (اختياري)

### 🖨️ الطباعة والتقارير
- طباعة فواتير احترافية
- تخصيص تخطيط الطباعة
- دعم الطابعات الحرارية (80mm)
- حفظ الفواتير محلياً ومع API

### 🌐 التكامل مع API
- اتصال مع خادم خارجي لمزامنة البيانات
- عمل بدون اتصال مع بيانات محفوظة محلياً
- مصادقة آمنة مع رموز JWT
- نسخ احتياطية تلقائية

## 🎨 التصميم والواجهة

### المواصفات التقنية
- **نظام التشغيل:** Windows 7 فما فوق
- **الدقة:** 1024×768 وأعلى
- **اللغة:** عربية مع دعم RTL كامل
- **التصميم:** Material Design مع ألوان عصرية

### مكونات الواجهة
1. **الشريط العلوي:** معلومات الفاتورة والمستخدم
2. **الوسط:** جدول المبيعات وقائمة المنتجات
3. **الجانب الأيمن:** ملخص الفاتورة والحسابات
4. **الشريط السفلي:** أزرار العمليات الرئيسية

## 🚀 التشغيل والتثبيت

### المتطلبات
- .NET 6.0 Runtime أو أحدث
- Windows 7 SP1 أو أحدث
- 2 GB RAM كحد أدنى
- 100 MB مساحة تخزين

### خطوات التشغيل

1. **تثبيت .NET 6.0:**
   ```
   تحميل من: https://dotnet.microsoft.com/download/dotnet/6.0
   ```

2. **بناء المشروع:**
   ```bash
   dotnet build
   ```

3. **تشغيل التطبيق:**
   ```bash
   dotnet run
   ```

### بيانات تسجيل الدخول الافتراضية
- **اسم المستخدم:** admin
- **كلمة المرور:** أي كلمة مرور (للاختبار)

## ⚙️ الإعدادات

### ملف appsettings.json
```json
{
  "ApiSettings": {
    "BaseUrl": "https://api.yourpos.com",
    "ApiKey": "your-api-key-here"
  },
  "AppSettings": {
    "TaxRate": 0.15,
    "Currency": "ريال",
    "CurrencySymbol": "ر.س"
  }
}
```

## 📁 هيكل المشروع

```
POSSystem/
├── Models/           # نماذج البيانات
│   ├── User.cs      # المستخدمين والصلاحيات
│   ├── Product.cs   # المنتجات والفئات
│   └── Invoice.cs   # الفواتير والعناصر
├── Services/         # طبقة الخدمات
│   ├── ApiService.cs      # خدمة API الأساسية
│   ├── UserService.cs     # خدمة المستخدمين
│   ├── ProductService.cs  # خدمة المنتجات
│   ├── InvoiceService.cs  # خدمة الفواتير
│   └── PrintService.cs    # خدمة الطباعة
├── Forms/            # النماذج والواجهات
│   ├── LoginForm.cs       # شاشة تسجيل الدخول
│   ├── MainPOSForm.cs     # الشاشة الرئيسية
│   └── PaymentForm.cs     # شاشة الدفع
└── Program.cs        # نقطة البداية
```

## 🔧 الوظائف المتقدمة

### اختصارات لوحة المفاتيح
- **F1:** فاتورة جديدة
- **F2:** حفظ الفاتورة
- **F3:** طباعة
- **Delete:** حذف العنصر المحدد
- **Enter:** إضافة منتج بالباركود

### نظام الصلاحيات
- **تعديل الأسعار:** يتطلب صلاحية خاصة
- **تطبيق خصومات:** محدود بنسبة ومبلغ أقصى
- **حذف العناصر:** يتطلب تأكيد
- **إلغاء الفواتير:** للمديرين فقط

### العمل بدون اتصال
- حفظ البيانات محلياً
- مزامنة تلقائية عند توفر الاتصال
- بيانات تجريبية للاختبار

## 🛠️ التطوير والتخصيص

### إضافة منتجات جديدة
يمكن إضافة منتجات من خلال:
1. API الخارجي
2. تعديل البيانات التجريبية في `ApiService.cs`
3. ملفات JSON محلية

### تخصيص التصميم
- تعديل الألوان في ملفات النماذج
- تغيير الخطوط والأحجام
- إضافة شعار الشركة

### إضافة تقارير
- تطوير نماذج جديدة للتقارير
- استخدام `PrintService` للطباعة
- تصدير البيانات بصيغ مختلفة

## 📞 الدعم والمساعدة

### المشاكل الشائعة
1. **خطأ في الاتصال بـ API:** تحقق من إعدادات الشبكة
2. **مشاكل الطباعة:** تأكد من تثبيت برامج تشغيل الطابعة
3. **بطء في التحميل:** تحقق من مساحة القرص الصلب

### التحديثات المستقبلية
- دعم قواعد بيانات محلية
- تقارير متقدمة ولوحة تحكم
- تطبيق جوال مصاحب
- دعم أنظمة الولاء والنقاط

## 📄 الترخيص

هذا المشروع مطور لأغراض تعليمية وتجارية. يمكن استخدامه وتعديله حسب الحاجة.

---

**تم التطوير بواسطة:** Augment Agent  
**التاريخ:** 2024  
**الإصدار:** 1.0.0
