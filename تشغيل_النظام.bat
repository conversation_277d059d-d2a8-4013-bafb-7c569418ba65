@echo off
chcp 65001 > nul
title نظام نقطة البيع - POS System

echo ========================================
echo        نظام نقطة البيع الاحترافي
echo              الإصدار 1.1
echo ========================================
echo.
echo المميزات الجديدة:
echo ✅ ضرائب متغيرة على مستوى المنتج
echo ✅ عمل بدون اتصال مع مزامنة ذكية
echo ✅ مؤشر حالة الاتصال المرئي
echo.

echo جاري التحقق من متطلبات النظام...
echo.

REM التحقق من وجود .NET
dotnet --version > nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ .NET غير مثبت على النظام
    echo.
    echo لتشغيل النظام، يرجى تثبيت .NET 6.0 أو أحدث:
    echo.
    echo 🔗 رابط التحميل:
    echo https://dotnet.microsoft.com/download/dotnet/6.0
    echo.
    echo 📋 خطوات التثبيت:
    echo 1. اضغط على الرابط أعلاه
    echo 2. اختر "Download .NET 6.0 Runtime" للاستخدام العادي
    echo 3. أو اختر "Download .NET 6.0 SDK" للتطوير
    echo 4. قم بتشغيل الملف المحمل واتبع التعليمات
    echo 5. أعد تشغيل هذا الملف بعد التثبيت
    echo.
    echo 💡 نصيحة: اختر النسخة المناسبة لنظام التشغيل:
    echo    - Windows x64 للأنظمة 64-bit
    echo    - Windows x86 للأنظمة 32-bit
    echo.
    pause
    exit /b 1
)

echo ✅ .NET مثبت بنجاح
dotnet --version
echo.

echo جاري بناء المشروع...
dotnet build --configuration Release > build.log 2>&1
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء المشروع
    echo.
    echo تفاصيل الخطأ:
    type build.log
    echo.
    echo 🔧 حلول مقترحة:
    echo 1. تأكد من تثبيت .NET SDK (وليس Runtime فقط)
    echo 2. تأكد من وجود جميع ملفات المشروع
    echo 3. جرب تشغيل الأمر: dotnet restore
    echo.
    pause
    exit /b 1
)

echo ✅ تم بناء المشروع بنجاح
echo.

echo 🚀 جاري تشغيل نظام نقطة البيع...
echo.
echo 🔐 بيانات تسجيل الدخول الافتراضية:
echo    اسم المستخدم: admin
echo    كلمة المرور: أي كلمة مرور
echo.
echo 💡 نصائح للاستخدام:
echo    • راقب مؤشر حالة الاتصال في الشريط العلوي
echo    • جرب إضافة منتجات بطرق مختلفة (كود، باركود، أزرار)
echo    • لاحظ الضرائب المختلفة لكل منتج
echo    • اضغط زر "مزامنة" لاختبار نظام العمل بدون اتصال
echo.

REM تشغيل التطبيق
dotnet run --configuration Release

echo.
echo تم إغلاق النظام
echo شكراً لاستخدام نظام نقطة البيع الاحترافي!
pause
