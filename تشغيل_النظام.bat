@echo off
chcp 65001 > nul
title نظام نقطة البيع - POS System

echo ========================================
echo        نظام نقطة البيع الاحترافي
echo ========================================
echo.

echo جاري التحقق من متطلبات النظام...
echo.

REM التحقق من وجود .NET
dotnet --version > nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ .NET غير مثبت على النظام
    echo.
    echo يرجى تثبيت .NET 6.0 أو أحدث من:
    echo https://dotnet.microsoft.com/download/dotnet/6.0
    echo.
    pause
    exit /b 1
)

echo ✅ .NET مثبت بنجاح
echo.

echo جاري بناء المشروع...
dotnet build --configuration Release > nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء المشروع
    echo.
    echo جاري المحاولة مع عرض التفاصيل...
    dotnet build --configuration Release
    pause
    exit /b 1
)

echo ✅ تم بناء المشروع بنجاح
echo.

echo جاري تشغيل نظام نقطة البيع...
echo.
echo بيانات تسجيل الدخول الافتراضية:
echo اسم المستخدم: admin
echo كلمة المرور: أي كلمة مرور
echo.

REM تشغيل التطبيق
dotnet run --configuration Release

echo.
echo تم إغلاق النظام
pause
