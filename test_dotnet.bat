@echo off
echo Testing .NET installation...

REM Test different possible locations
if exist "C:\Program Files\dotnet\dotnet.exe" (
    echo Found .NET at: C:\Program Files\dotnet\
    "C:\Program Files\dotnet\dotnet.exe" --version
    goto :found
)

if exist "C:\Program Files (x86)\dotnet\dotnet.exe" (
    echo Found .NET at: C:\Program Files (x86)\dotnet\
    "C:\Program Files (x86)\dotnet\dotnet.exe" --version
    goto :found
)

echo .NET not found in standard locations
goto :end

:found
echo .NET is working!
echo Now trying to build the project...
"C:\Program Files\dotnet\dotnet.exe" build --configuration Release

:end
pause
