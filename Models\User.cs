using System.ComponentModel.DataAnnotations;

namespace POSSystem.Models
{
    /// <summary>
    /// نموذج المستخدم
    /// </summary>
    public class User
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(50)]
        public string Username { get; set; } = string.Empty;
        
        [Required]
        [StringLength(100)]
        public string FullName { get; set; } = string.Empty;
        
        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;
        
        public string? Phone { get; set; }
        
        public UserRole Role { get; set; }
        
        public UserPermissions Permissions { get; set; } = new();
        
        public bool IsActive { get; set; } = true;
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        public DateTime? LastLogin { get; set; }
        
        public string? ProfileImage { get; set; }
    }

    /// <summary>
    /// أدوار المستخدمين
    /// </summary>
    public enum UserRole
    {
        Admin = 1,      // مدير النظام
        Manager = 2,    // مدير المتجر
        Cashier = 3,    // أمين الصندوق
        Employee = 4    // موظف
    }

    /// <summary>
    /// صلاحيات المستخدم
    /// </summary>
    public class UserPermissions
    {
        public bool CanEditPrices { get; set; } = false;
        public bool CanApplyDiscounts { get; set; } = false;
        public bool CanDeleteItems { get; set; } = false;
        public bool CanVoidInvoices { get; set; } = false;
        public bool CanAccessReports { get; set; } = false;
        public bool CanManageUsers { get; set; } = false;
        public bool CanManageProducts { get; set; } = false;
        public bool CanCloseCash { get; set; } = false;
        public bool CanOpenCash { get; set; } = false;
        public bool CanPrintReports { get; set; } = false;
        public decimal MaxDiscountPercentage { get; set; } = 0;
        public decimal MaxDiscountAmount { get; set; } = 0;
    }

    /// <summary>
    /// نموذج تسجيل الدخول
    /// </summary>
    public class LoginRequest
    {
        [Required]
        public string Username { get; set; } = string.Empty;
        
        [Required]
        public string Password { get; set; } = string.Empty;
        
        public bool RememberMe { get; set; } = false;
    }

    /// <summary>
    /// استجابة تسجيل الدخول
    /// </summary>
    public class LoginResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public User? User { get; set; }
        public string? Token { get; set; }
        public DateTime? ExpiresAt { get; set; }
    }
}
