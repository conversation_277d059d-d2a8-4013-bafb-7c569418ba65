using POSSystem.Models;

namespace POSSystem.Services
{
    /// <summary>
    /// واجهة خدمة API الأساسية
    /// </summary>
    public interface IApiService
    {
        /// <summary>
        /// إرسال طلب GET
        /// </summary>
        Task<T?> GetAsync<T>(string endpoint);
        
        /// <summary>
        /// إرسال طلب POST
        /// </summary>
        Task<T?> PostAsync<T>(string endpoint, object data);
        
        /// <summary>
        /// إرسال طلب PUT
        /// </summary>
        Task<T?> PutAsync<T>(string endpoint, object data);
        
        /// <summary>
        /// إرسال طلب DELETE
        /// </summary>
        Task<bool> DeleteAsync(string endpoint);
        
        /// <summary>
        /// تعيين رمز المصادقة
        /// </summary>
        void SetAuthToken(string token);
        
        /// <summary>
        /// إزالة رمز المصادقة
        /// </summary>
        void ClearAuthToken();
    }

    /// <summary>
    /// واجهة خدمة المستخدمين
    /// </summary>
    public interface IUserService
    {
        /// <summary>
        /// تسجيل الدخول
        /// </summary>
        Task<LoginResponse> LoginAsync(LoginRequest request);
        
        /// <summary>
        /// تسجيل الخروج
        /// </summary>
        Task<bool> LogoutAsync();
        
        /// <summary>
        /// الحصول على المستخدم الحالي
        /// </summary>
        User? GetCurrentUser();
        
        /// <summary>
        /// التحقق من صلاحية المستخدم
        /// </summary>
        bool HasPermission(string permission);
        
        /// <summary>
        /// تحديث معلومات المستخدم
        /// </summary>
        Task<bool> UpdateUserAsync(User user);
    }

    /// <summary>
    /// واجهة خدمة المنتجات
    /// </summary>
    public interface IProductService
    {
        /// <summary>
        /// البحث عن المنتجات
        /// </summary>
        Task<List<Product>> SearchProductsAsync(ProductSearchRequest request);
        
        /// <summary>
        /// الحصول على منتج بالكود
        /// </summary>
        Task<Product?> GetProductByCodeAsync(string code);
        
        /// <summary>
        /// الحصول على منتج بالباركود
        /// </summary>
        Task<Product?> GetProductByBarcodeAsync(string barcode);
        
        /// <summary>
        /// الحصول على جميع الفئات
        /// </summary>
        Task<List<Category>> GetCategoriesAsync();
        
        /// <summary>
        /// الحصول على منتجات الفئة
        /// </summary>
        Task<List<Product>> GetProductsByCategoryAsync(int categoryId);
        
        /// <summary>
        /// تحديث مخزون المنتج
        /// </summary>
        Task<bool> UpdateProductStockAsync(int productId, decimal quantity);
    }

    /// <summary>
    /// واجهة خدمة الفواتير
    /// </summary>
    public interface IInvoiceService
    {
        /// <summary>
        /// حفظ الفاتورة
        /// </summary>
        Task<bool> SaveInvoiceAsync(Invoice invoice);
        
        /// <summary>
        /// الحصول على رقم فاتورة جديد
        /// </summary>
        Task<string> GetNewInvoiceNumberAsync();
        
        /// <summary>
        /// البحث عن الفواتير
        /// </summary>
        Task<List<Invoice>> SearchInvoicesAsync(DateTime? fromDate = null, DateTime? toDate = null);
        
        /// <summary>
        /// إلغاء الفاتورة
        /// </summary>
        Task<bool> CancelInvoiceAsync(int invoiceId);
        
        /// <summary>
        /// طباعة الفاتورة
        /// </summary>
        Task<bool> PrintInvoiceAsync(Invoice invoice);

        /// <summary>
        /// مزامنة البيانات المحفوظة بدون اتصال
        /// </summary>
        Task<bool> SyncOfflineDataAsync();

        /// <summary>
        /// الحصول على عدد الفواتير المعلقة
        /// </summary>
        Task<int> GetPendingInvoicesCountAsync();
    }

    /// <summary>
    /// واجهة خدمة الطباعة
    /// </summary>
    public interface IPrintService
    {
        /// <summary>
        /// طباعة الفاتورة
        /// </summary>
        Task<bool> PrintInvoiceAsync(Invoice invoice);
        
        /// <summary>
        /// طباعة تقرير
        /// </summary>
        Task<bool> PrintReportAsync(string reportContent);
        
        /// <summary>
        /// الحصول على قائمة الطابعات المتاحة
        /// </summary>
        List<string> GetAvailablePrinters();
        
        /// <summary>
        /// تعيين الطابعة الافتراضية
        /// </summary>
        void SetDefaultPrinter(string printerName);
    }
}
