@echo off
chcp 65001 > nul
title اختبار النظام بعد تثبيت SDK

echo ========================================
echo         اختبار النظام الكامل
echo ========================================
echo.

echo 🔍 التحقق من .NET SDK...
dotnet --version 2>nul
if %errorlevel% neq 0 (
    echo ❌ .NET SDK غير مثبت بعد
    echo يرجى إكمال تثبيت SDK وإعادة تشغيل الكمبيوتر
    pause
    exit /b 1
)

echo ✅ .NET SDK مثبت بنجاح!
echo الإصدار: 
dotnet --version
echo.

echo 🔨 بناء المشروع...
dotnet build --configuration Release
if %errorlevel% neq 0 (
    echo ❌ فشل في بناء المشروع
    echo تحقق من وجود جميع الملفات
    pause
    exit /b 1
)

echo ✅ تم بناء المشروع بنجاح!
echo.

echo 🚀 تشغيل نظام نقطة البيع الكامل...
echo.
echo 🔐 بيانات الدخول:
echo    المستخدم: admin
echo    كلمة المرور: أي كلمة مرور
echo.

dotnet run --configuration Release
