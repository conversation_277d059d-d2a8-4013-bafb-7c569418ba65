@echo off
setlocal enabledelayedexpansion

echo ========================================
echo        POS System - نظام نقطة البيع
echo              Version 1.1
echo ========================================
echo.

echo Checking .NET installation...

REM Check for .NET in standard locations
set DOTNET_PATH=
if exist "C:\Program Files\dotnet\dotnet.exe" (
    set DOTNET_PATH=C:\Program Files\dotnet\dotnet.exe
    goto :found
)

if exist "C:\Program Files (x86)\dotnet\dotnet.exe" (
    set DOTNET_PATH=C:\Program Files ^(x86^)\dotnet\dotnet.exe
    goto :found
)

echo ❌ .NET not found in standard locations!
echo Please install .NET 6.0 SDK from:
echo https://dotnet.microsoft.com/download/dotnet/6.0
pause
exit /b 1

:found
echo ✅ .NET found at: !DOTNET_PATH!

REM Get version
echo Version:
"!DOTNET_PATH!" --version
echo.

echo Building project...
"!DOTNET_PATH!" build POSSystem.csproj --configuration Release

if !errorlevel! neq 0 (
    echo ❌ Build failed!
    echo Check the error messages above.
    pause
    exit /b 1
)

echo ✅ Build successful!
echo.

echo 🚀 Starting POS System...
echo.
echo 🔐 Login credentials:
echo    Username: admin
echo    Password: any password
echo.

REM Run the application
"!DOTNET_PATH!" run --project POSSystem.csproj --configuration Release

echo.
echo System closed.
pause
