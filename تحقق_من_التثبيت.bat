@echo off
chcp 65001 > nul
title التحقق من تثبيت .NET

echo ========================================
echo         التحقق من تثبيت .NET
echo ========================================
echo.

echo جاري التحقق من .NET...
echo.

dotnet --version > nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ .NET غير مثبت أو غير متاح
    echo.
    echo يرجى:
    echo 1. تحميل .NET 6.0 من الرابط المفتوح
    echo 2. تثبيته واتباع التعليمات
    echo 3. إعادة تشغيل الكمبيوتر
    echo 4. تشغيل هذا الملف مرة أخرى
    echo.
    pause
    exit /b 1
)

echo ✅ .NET مثبت بنجاح!
echo.
echo الإصدار المثبت:
dotnet --version
echo.

echo جاري التحقق من Windows Forms...
dotnet --list-runtimes | findstr "Microsoft.WindowsDesktop.App" > nul
if %errorlevel% neq 0 (
    echo ⚠️  Windows Desktop Runtime غير مثبت
    echo يرجى تحميل "Desktop Runtime" بدلاً من "Runtime" العادي
    echo.
    pause
    exit /b 1
)

echo ✅ Windows Desktop Runtime متاح
echo.

echo 🎉 النظام جاهز للتشغيل!
echo.
echo اضغط أي مفتاح لتشغيل نظام نقطة البيع...
pause

REM تشغيل النظام
call "تشغيل_النظام.bat"
