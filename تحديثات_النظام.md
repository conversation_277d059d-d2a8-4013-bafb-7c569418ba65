# تحديثات نظام نقطة البيع - الإصدار 1.1

## 🆕 المميزات الجديدة

### 1. 💰 نظام الضرائب المتقدم على مستوى المنتج

#### الضرائب المتغيرة
- **ضريبة مخصصة لكل منتج:** يتم جلب نسبة الضريبة من API لكل منتج على حدة
- **نسب ضريبة متنوعة:** 0%, 5%, 10%, 15% أو أي نسبة أخرى
- **منتجات معفاة من الضريبة:** إمكانية إعفاء منتجات معينة من الضريبة

#### الضرائب الشاملة وغير الشاملة
- **ضريبة غير شاملة:** السعر المعروض + الضريبة = السعر النهائي
- **ضريبة شاملة:** السعر المعروض يشمل الضريبة بالفعل
- **حساب تلقائي:** النظام يحسب السعر الأساسي والضريبة تلقائياً

#### أمثلة عملية:
```
منتج بضريبة غير شاملة (15%):
- السعر المعروض: 100 ر.س
- الضريبة: 15 ر.س
- السعر النهائي: 115 ر.س

منتج بضريبة شاملة (15%):
- السعر المعروض: 115 ر.س
- السعر الأساسي: 100 ر.س
- الضريبة: 15 ر.س
```

### 2. 🔄 نظام العمل بدون اتصال المتطور

#### الحفظ الذكي
- **حفظ تلقائي:** عند عدم توفر الاتصال، يتم حفظ الفواتير محلياً
- **ملفات منفصلة:** كل فاتورة تُحفظ في ملف منفصل لضمان الأمان
- **طوابع زمنية:** تسجيل دقيق لوقت إنشاء كل فاتورة

#### المزامنة التلقائية
- **فحص دوري:** كل دقيقة يتم فحص حالة الاتصال
- **إرسال تلقائي:** عند توفر الاتصال، يتم إرسال الفواتير المعلقة تلقائياً
- **إعادة المحاولة:** في حالة فشل الإرسال، يتم المحاولة مرة أخرى لاحقاً

#### مؤشر الحالة المرئي
- **🔄 مزامنة (X معلق):** يوجد فواتير معلقة وجاري المزامنة
- **✅ متصل:** متصل بالإنترنت ولا توجد فواتير معلقة
- **❌ غير متصل (X معلق):** غير متصل ويوجد فواتير معلقة
- **❌ غير متصل:** غير متصل ولا توجد فواتير معلقة

### 3. 🔧 أدوات المزامنة اليدوية

#### زر المزامنة
- **مزامنة فورية:** إمكانية إجراء مزامنة يدوية فورية
- **عرض التقدم:** إظهار عدد الفواتير المعلقة قبل المزامنة
- **تأكيد المستخدم:** طلب تأكيد قبل بدء المزامنة

#### تقارير المزامنة
- **سجل المزامنة:** تسجيل جميع عمليات المزامنة في ملف منفصل
- **سجل الأخطاء:** تسجيل أي أخطاء تحدث أثناء المزامنة
- **إحصائيات:** عرض عدد الفواتير المرسلة بنجاح والفاشلة

## 🔧 التحسينات التقنية

### هيكل البيانات المحسن
```csharp
public class Product
{
    public decimal TaxRate { get; set; }        // نسبة الضريبة
    public bool IsTaxable { get; set; }         // خاضع للضريبة
    public bool IsTaxInclusive { get; set; }    // ضريبة شاملة
    
    public decimal BasePrice { get; }           // السعر الأساسي
    public decimal TaxAmount { get; }           // قيمة الضريبة
    public decimal FinalPrice { get; }          // السعر النهائي
}
```

### خدمة البيانات بدون اتصال
```csharp
public interface IOfflineDataService
{
    Task SaveOfflineInvoiceAsync(Invoice invoice);
    Task<bool> SyncOfflineDataAsync();
    Task<bool> IsOnlineAsync();
    Task<int> GetPendingInvoicesCountAsync();
}
```

### نظام الملفات المحسن
```
AppData/POSSystem/
├── OfflineData/
│   ├── PendingInvoices/
│   │   ├── invoice_INV20241217001_20241217143022.json
│   │   ├── invoice_INV20241217002_20241217143155.json
│   │   └── ...
│   ├── sync.log          # سجل المزامنة
│   └── errors.log        # سجل الأخطاء
└── Invoices/             # نسخ احتياطية
```

## 📊 أمثلة عملية للضرائب

### منتجات بضرائب مختلفة:
1. **خبز أبيض:** 15% ضريبة غير شاملة
2. **حليب طازج:** 5% ضريبة شاملة  
3. **أرز بسمتي:** 10% ضريبة غير شاملة
4. **زيت زيتون:** 15% ضريبة شاملة
5. **سكر أبيض:** معفى من الضريبة (0%)

### حساب الفاتورة:
```
العنصر              الكمية    السعر      الضريبة    المجموع
خبز أبيض (15%)        2      2.50      0.75       5.75
حليب (5% شامل)        1      8.00      0.38       8.00
سكر (معفى)            1     12.00      0.00      12.00
                                      ------     ------
المجموع الفرعي:                        1.13      25.75
الإجمالي:                                       25.75
```

## 🚀 طريقة الاستخدام

### تفعيل الضرائب المتنوعة
1. في ملف `appsettings.json`، تأكد من إعداد API الضرائب
2. كل منتج سيحصل على إعدادات الضريبة من API
3. النظام سيحسب الضرائب تلقائياً حسب نوع كل منتج

### استخدام نظام العمل بدون اتصال
1. **تلقائي:** النظام يعمل تلقائياً بدون تدخل
2. **مراقبة الحالة:** راقب مؤشر الحالة في الشريط العلوي
3. **مزامنة يدوية:** اضغط زر "مزامنة" عند الحاجة

### مراقبة الفواتير المعلقة
- **العدد:** يظهر في مؤشر الحالة
- **التفاصيل:** في سجل المزامنة
- **الإرسال:** يتم تلقائياً عند توفر الاتصال

## 🔍 استكشاف الأخطاء

### مشاكل الضرائب
- **ضريبة خاطئة:** تحقق من إعدادات المنتج في API
- **حساب خاطئ:** تأكد من صحة نسبة الضريبة ونوعها

### مشاكل المزامنة
- **فشل الإرسال:** تحقق من اتصال الإنترنت وإعدادات API
- **فواتير معلقة:** استخدم زر المزامنة اليدوية
- **أخطاء متكررة:** راجع ملف `errors.log`

## 📈 الإحصائيات والمراقبة

### ملفات السجل
- **sync.log:** سجل عمليات المزامنة الناجحة
- **errors.log:** سجل الأخطاء والمشاكل
- **invoice_*.json:** الفواتير المحفوظة محلياً

### مراقبة الأداء
- **وقت المزامنة:** يتم تسجيله في السجل
- **معدل النجاح:** نسبة الفواتير المرسلة بنجاح
- **الفواتير المعلقة:** العدد الحالي للفواتير غير المرسلة

## 🎯 الفوائد الجديدة

### للمستخدم
- **مرونة في الضرائب:** دعم أنواع مختلفة من الضرائب
- **عمل مستمر:** لا توقف في العمل حتى بدون إنترنت
- **شفافية:** معرفة حالة النظام والبيانات المعلقة

### للإدارة
- **دقة الحسابات:** حساب دقيق للضرائب حسب نوع المنتج
- **أمان البيانات:** عدم فقدان أي فاتورة حتى بدون اتصال
- **مراقبة شاملة:** سجلات مفصلة لجميع العمليات

### للنظام
- **موثوقية عالية:** عمل مستمر في جميع الظروف
- **مزامنة ذكية:** إرسال تلقائي عند توفر الاتصال
- **أداء محسن:** معالجة فعالة للبيانات المحلية

---

**الإصدار:** 1.1  
**تاريخ التحديث:** ديسمبر 2024  
**الحالة:** جاهز للاستخدام ✅
