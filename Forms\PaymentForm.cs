using POSSystem.Models;

namespace POSSystem.Forms
{
    public partial class PaymentForm : Form
    {
        public decimal PaidAmount { get; private set; }
        public PaymentMethod PaymentMethod { get; private set; }
        
        private readonly decimal _totalAmount;
        private TextBox _paidAmountTextBox;
        private ComboBox _paymentMethodComboBox;
        private Label _changeLabel;
        private Label _totalLabel;

        public PaymentForm(decimal totalAmount)
        {
            _totalAmount = totalAmount;
            InitializeComponent();
            SetupForm();
        }

        private void SetupForm()
        {
            this.Text = "الدفع";
            this.Size = new Size(400, 300);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.White;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            CreateControls();
        }

        private void CreateControls()
        {
            var y = 20;
            var lineHeight = 40;

            // عنوان
            var titleLabel = new Label
            {
                Text = "إتمام عملية الدفع",
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                Size = new Size(300, 30),
                Location = new Point(50, y),
                TextAlign = ContentAlignment.MiddleCenter,
                ForeColor = Color.FromArgb(52, 152, 219)
            };
            y += lineHeight;

            // المبلغ الإجمالي
            var totalTitleLabel = new Label
            {
                Text = "المبلغ الإجمالي:",
                Font = new Font("Segoe UI", 11),
                Size = new Size(120, 25),
                Location = new Point(250, y),
                TextAlign = ContentAlignment.MiddleRight
            };

            _totalLabel = new Label
            {
                Text = $"{_totalAmount:F2} ر.س",
                Font = new Font("Segoe UI", 11, FontStyle.Bold),
                Size = new Size(100, 25),
                Location = new Point(140, y),
                TextAlign = ContentAlignment.MiddleLeft,
                ForeColor = Color.FromArgb(46, 204, 113)
            };
            y += lineHeight;

            // طريقة الدفع
            var paymentMethodLabel = new Label
            {
                Text = "طريقة الدفع:",
                Font = new Font("Segoe UI", 11),
                Size = new Size(120, 25),
                Location = new Point(250, y),
                TextAlign = ContentAlignment.MiddleRight
            };

            _paymentMethodComboBox = new ComboBox
            {
                Font = new Font("Segoe UI", 11),
                Size = new Size(150, 30),
                Location = new Point(90, y),
                DropDownStyle = ComboBoxStyle.DropDownList
            };

            // إضافة طرق الدفع
            _paymentMethodComboBox.Items.Add(new { Text = "نقدي", Value = PaymentMethod.Cash });
            _paymentMethodComboBox.Items.Add(new { Text = "بطاقة ائتمان", Value = PaymentMethod.CreditCard });
            _paymentMethodComboBox.Items.Add(new { Text = "بطاقة خصم", Value = PaymentMethod.DebitCard });
            _paymentMethodComboBox.Items.Add(new { Text = "تحويل بنكي", Value = PaymentMethod.BankTransfer });
            
            _paymentMethodComboBox.DisplayMember = "Text";
            _paymentMethodComboBox.ValueMember = "Value";
            _paymentMethodComboBox.SelectedIndex = 0; // نقدي كافتراضي
            y += lineHeight;

            // المبلغ المدفوع
            var paidAmountLabel = new Label
            {
                Text = "المبلغ المدفوع:",
                Font = new Font("Segoe UI", 11),
                Size = new Size(120, 25),
                Location = new Point(250, y),
                TextAlign = ContentAlignment.MiddleRight
            };

            _paidAmountTextBox = new TextBox
            {
                Font = new Font("Segoe UI", 11),
                Size = new Size(150, 30),
                Location = new Point(90, y),
                Text = _totalAmount.ToString("F2")
            };
            _paidAmountTextBox.TextChanged += PaidAmountTextBox_TextChanged;
            _paidAmountTextBox.KeyPress += PaidAmountTextBox_KeyPress;
            y += lineHeight;

            // الباقي
            var changeLabel = new Label
            {
                Text = "الباقي:",
                Font = new Font("Segoe UI", 11),
                Size = new Size(120, 25),
                Location = new Point(250, y),
                TextAlign = ContentAlignment.MiddleRight
            };

            _changeLabel = new Label
            {
                Text = "0.00 ر.س",
                Font = new Font("Segoe UI", 11, FontStyle.Bold),
                Size = new Size(150, 25),
                Location = new Point(90, y),
                TextAlign = ContentAlignment.MiddleLeft,
                ForeColor = Color.FromArgb(231, 76, 60)
            };
            y += lineHeight + 20;

            // أزرار
            var okButton = new Button
            {
                Text = "موافق",
                Font = new Font("Segoe UI", 11, FontStyle.Bold),
                Size = new Size(80, 35),
                Location = new Point(200, y),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                DialogResult = DialogResult.OK
            };
            okButton.FlatAppearance.BorderSize = 0;
            okButton.Click += OkButton_Click;

            var cancelButton = new Button
            {
                Text = "إلغاء",
                Font = new Font("Segoe UI", 11, FontStyle.Bold),
                Size = new Size(80, 35),
                Location = new Point(110, y),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                DialogResult = DialogResult.Cancel
            };
            cancelButton.FlatAppearance.BorderSize = 0;

            // إضافة الكنترولز
            this.Controls.AddRange(new Control[]
            {
                titleLabel, totalTitleLabel, _totalLabel,
                paymentMethodLabel, _paymentMethodComboBox,
                paidAmountLabel, _paidAmountTextBox,
                changeLabel, _changeLabel,
                okButton, cancelButton
            });

            // تعيين التركيز
            _paidAmountTextBox.Focus();
            _paidAmountTextBox.SelectAll();

            // حساب الباقي الأولي
            CalculateChange();
        }

        private void PaidAmountTextBox_KeyPress(object sender, KeyPressEventArgs e)
        {
            // السماح فقط بالأرقام والفاصلة العشرية
            if (!char.IsControl(e.KeyChar) && !char.IsDigit(e.KeyChar) && e.KeyChar != '.')
            {
                e.Handled = true;
            }

            // السماح بفاصلة عشرية واحدة فقط
            if (e.KeyChar == '.' && _paidAmountTextBox.Text.Contains('.'))
            {
                e.Handled = true;
            }

            // معالجة Enter
            if (e.KeyChar == (char)Keys.Enter)
            {
                OkButton_Click(sender, EventArgs.Empty);
                e.Handled = true;
            }
        }

        private void PaidAmountTextBox_TextChanged(object sender, EventArgs e)
        {
            CalculateChange();
        }

        private void CalculateChange()
        {
            if (decimal.TryParse(_paidAmountTextBox.Text, out decimal paidAmount))
            {
                var change = paidAmount - _totalAmount;
                _changeLabel.Text = $"{Math.Max(0, change):F2} ر.س";
                
                if (change < 0)
                {
                    _changeLabel.ForeColor = Color.FromArgb(231, 76, 60); // أحمر للمبلغ الناقص
                    _changeLabel.Text = $"{Math.Abs(change):F2} ر.س (ناقص)";
                }
                else
                {
                    _changeLabel.ForeColor = Color.FromArgb(46, 204, 113); // أخضر للباقي
                }
            }
            else
            {
                _changeLabel.Text = "0.00 ر.س";
                _changeLabel.ForeColor = Color.FromArgb(231, 76, 60);
            }
        }

        private void OkButton_Click(object sender, EventArgs e)
        {
            if (!decimal.TryParse(_paidAmountTextBox.Text, out decimal paidAmount))
            {
                MessageBox.Show("يرجى إدخال مبلغ صحيح", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                _paidAmountTextBox.Focus();
                return;
            }

            if (paidAmount < _totalAmount)
            {
                var result = MessageBox.Show($"المبلغ المدفوع أقل من الإجمالي بـ {_totalAmount - paidAmount:F2} ر.س\nهل تريد المتابعة؟", 
                    "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                
                if (result != DialogResult.Yes)
                {
                    _paidAmountTextBox.Focus();
                    return;
                }
            }

            PaidAmount = paidAmount;
            
            var selectedItem = _paymentMethodComboBox.SelectedItem;
            if (selectedItem != null)
            {
                PaymentMethod = (PaymentMethod)((dynamic)selectedItem).Value;
            }
            else
            {
                PaymentMethod = PaymentMethod.Cash;
            }

            this.DialogResult = DialogResult.OK;
            this.Close();
        }
    }
}
