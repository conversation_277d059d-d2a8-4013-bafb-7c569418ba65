using POSSystem.Models;

namespace POSSystem.Services
{
    /// <summary>
    /// خدمة الفواتير
    /// </summary>
    public class InvoiceService : IInvoiceService
    {
        private readonly IApiService _apiService;
        private readonly IPrintService _printService;
        private readonly IOfflineDataService _offlineDataService;

        public InvoiceService(IApiService apiService, IPrintService printService, IOfflineDataService offlineDataService)
        {
            _apiService = apiService;
            _printService = printService;
            _offlineDataService = offlineDataService;
        }

        public async Task<bool> SaveInvoiceAsync(Invoice invoice)
        {
            try
            {
                // حساب إجمالي الفاتورة قبل الحفظ
                invoice.CalculateTotal();

                // التحقق من حالة الاتصال
                var isOnline = await _offlineDataService.IsOnlineAsync();

                if (isOnline)
                {
                    try
                    {
                        var result = await _apiService.PostAsync<Invoice>("invoices", invoice);

                        if (result != null)
                        {
                            // نجح الإرسال، حفظ نسخة احتياطية محلياً
                            SaveInvoiceLocally(result);
                            return true;
                        }
                    }
                    catch (Exception)
                    {
                        // فشل الإرسال رغم وجود اتصال، حفظ بدون اتصال
                    }
                }

                // حفظ الفاتورة بدون اتصال للإرسال لاحقاً
                await _offlineDataService.SaveOfflineInvoiceAsync(invoice);

                // حفظ نسخة احتياطية محلياً أيضاً
                SaveInvoiceLocally(invoice);

                return true;
            }
            catch (Exception)
            {
                // في حالة فشل كل شيء، حفظ محلياً على الأقل
                SaveInvoiceLocally(invoice);
                return true;
            }
        }

        public async Task<string> GetNewInvoiceNumberAsync()
        {
            try
            {
                var response = await _apiService.GetAsync<dynamic>("invoices/new-number");
                
                if (response != null && response.invoiceNumber != null)
                {
                    return response.invoiceNumber.ToString();
                }
            }
            catch (Exception)
            {
                // تجاهل الخطأ والمتابعة لتوليد رقم محلي
            }
            
            // توليد رقم فاتورة محلي
            return GenerateLocalInvoiceNumber();
        }

        public async Task<List<Invoice>> SearchInvoicesAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                var searchData = new
                {
                    FromDate = fromDate,
                    ToDate = toDate
                };
                
                var invoices = await _apiService.PostAsync<List<Invoice>>("invoices/search", searchData);
                
                if (invoices != null)
                {
                    return invoices;
                }
            }
            catch (Exception)
            {
                // تجاهل الخطأ والمتابعة للبيانات المحلية
            }
            
            // البحث في الفواتير المحفوظة محلياً
            return LoadInvoicesLocally(fromDate, toDate);
        }

        public async Task<bool> CancelInvoiceAsync(int invoiceId)
        {
            try
            {
                var result = await _apiService.PostAsync<object>($"invoices/{invoiceId}/cancel", new { });
                return result != null;
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> PrintInvoiceAsync(Invoice invoice)
        {
            try
            {
                // طباعة الفاتورة
                var printResult = await _printService.PrintInvoiceAsync(invoice);
                
                if (printResult)
                {
                    // تحديث حالة الطباعة
                    invoice.IsPrinted = true;
                    invoice.PrintedAt = DateTime.Now;
                    
                    // حفظ التحديث
                    await SaveInvoiceAsync(invoice);
                }
                
                return printResult;
            }
            catch (Exception)
            {
                return false;
            }
        }

        private string GenerateLocalInvoiceNumber()
        {
            var today = DateTime.Now;
            var prefix = $"INV{today:yyyyMMdd}";
            
            // البحث عن آخر رقم فاتورة لهذا اليوم
            var todayInvoices = LoadInvoicesLocally(today.Date, today.Date.AddDays(1));
            var lastNumber = todayInvoices
                .Where(i => i.InvoiceNumber.StartsWith(prefix))
                .Select(i => i.InvoiceNumber)
                .OrderByDescending(n => n)
                .FirstOrDefault();
            
            int nextNumber = 1;
            if (!string.IsNullOrEmpty(lastNumber) && lastNumber.Length > prefix.Length)
            {
                var numberPart = lastNumber.Substring(prefix.Length);
                if (int.TryParse(numberPart, out int currentNumber))
                {
                    nextNumber = currentNumber + 1;
                }
            }
            
            return $"{prefix}{nextNumber:D4}";
        }

        private void SaveInvoiceLocally(Invoice invoice)
        {
            try
            {
                var invoicesDir = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "POSSystem", "Invoices");
                Directory.CreateDirectory(invoicesDir);
                
                var fileName = $"invoice_{invoice.InvoiceNumber}_{DateTime.Now:yyyyMMddHHmmss}.json";
                var filePath = Path.Combine(invoicesDir, fileName);
                
                var json = Newtonsoft.Json.JsonConvert.SerializeObject(invoice, Newtonsoft.Json.Formatting.Indented);
                File.WriteAllText(filePath, json);
            }
            catch (Exception)
            {
                // تجاهل أخطاء الحفظ
            }
        }

        private List<Invoice> LoadInvoicesLocally(DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                var invoicesDir = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "POSSystem", "Invoices");
                
                if (!Directory.Exists(invoicesDir))
                {
                    return new List<Invoice>();
                }
                
                var invoices = new List<Invoice>();
                var files = Directory.GetFiles(invoicesDir, "invoice_*.json");
                
                foreach (var file in files)
                {
                    try
                    {
                        var json = File.ReadAllText(file);
                        var invoice = Newtonsoft.Json.JsonConvert.DeserializeObject<Invoice>(json);
                        
                        if (invoice != null)
                        {
                            // تطبيق فلتر التاريخ
                            if (fromDate.HasValue && invoice.InvoiceDate.Date < fromDate.Value.Date)
                                continue;
                            
                            if (toDate.HasValue && invoice.InvoiceDate.Date > toDate.Value.Date)
                                continue;
                            
                            invoices.Add(invoice);
                        }
                    }
                    catch (Exception)
                    {
                        // تجاهل الملفات التالفة
                    }
                }
                
                return invoices.OrderByDescending(i => i.InvoiceDate).ToList();
            }
            catch (Exception)
            {
                return new List<Invoice>();
            }
        }

        public async Task<bool> SyncOfflineDataAsync()
        {
            return await _offlineDataService.SyncOfflineDataAsync();
        }

        public async Task<int> GetPendingInvoicesCountAsync()
        {
            return await _offlineDataService.GetPendingInvoicesCountAsync();
        }
    }
}
