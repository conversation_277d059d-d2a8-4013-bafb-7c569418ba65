using POSSystem.Models;
using POSSystem.Services;
using System.Data;

namespace POSSystem.Forms
{
    public partial class MainPOSForm : Form
    {
        private readonly IUserService _userService;
        private readonly IProductService _productService;
        private readonly IInvoiceService _invoiceService;
        private readonly IPrintService _printService;
        private readonly IOfflineDataService _offlineDataService;

        private Invoice _currentInvoice;
        private List<Product> _products = new();
        private List<Category> _categories = new();
        
        // Controls
        private DataGridView _salesGrid;
        private FlowLayoutPanel _productsPanel;
        private Panel _summaryPanel;
        private TextBox _barcodeTextBox;
        private Label _totalLabel;
        private Label _invoiceNumberLabel;
        private Label _userLabel;
        private Label _dateTimeLabel;
        private Label _syncStatusLabel;

        public MainPOSForm(IUserService userService, IProductService productService,
            IInvoiceService invoiceService, IPrintService printService, IOfflineDataService offlineDataService)
        {
            _userService = userService;
            _productService = productService;
            _invoiceService = invoiceService;
            _printService = printService;
            _offlineDataService = offlineDataService;
            
            InitializeComponent();
            SetupForm();
            InitializeNewInvoice();
            LoadData();
        }

        private void SetupForm()
        {
            // إعداد النموذج الرئيسي
            this.Text = "نظام نقطة البيع - الشاشة الرئيسية";
            this.WindowState = FormWindowState.Maximized;
            this.BackColor = Color.FromArgb(245, 245, 245);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.KeyPreview = true;

            CreateLayout();
            SetupEventHandlers();
        }

        private void CreateLayout()
        {
            // لوحة علوية - معلومات الفاتورة
            var topPanel = CreateTopPanel();
            
            // لوحة وسطى - المحتوى الرئيسي
            var mainPanel = CreateMainPanel();
            
            // لوحة سفلية - أزرار العمليات
            var bottomPanel = CreateBottomPanel();

            // إضافة اللوحات للنموذج
            this.Controls.Add(topPanel);
            this.Controls.Add(mainPanel);
            this.Controls.Add(bottomPanel);

            // ترتيب اللوحات
            topPanel.Dock = DockStyle.Top;
            bottomPanel.Dock = DockStyle.Bottom;
            mainPanel.Dock = DockStyle.Fill;
        }

        private Panel CreateTopPanel()
        {
            var panel = new Panel
            {
                Height = 50, // تصغير الهيدر
                BackColor = Color.FromArgb(52, 152, 219),
                Padding = new Padding(5)
            };

            // معلومات الفاتورة مضغوطة
            _invoiceNumberLabel = new Label
            {
                Text = "فاتورة: جاري التحميل...",
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(10, 15),
                AutoSize = true
            };

            _userLabel = new Label
            {
                Text = $"المستخدم: {_userService.GetCurrentUser()?.FullName ?? "غير محدد"}",
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.White,
                Location = new Point(200, 15),
                AutoSize = true
            };

            _dateTimeLabel = new Label
            {
                Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm"),
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.White,
                Location = new Point(400, 15),
                AutoSize = true
            };

            // مؤشر حالة المزامنة
            _syncStatusLabel = new Label
            {
                Text = "🔄 جاري التحقق...",
                Font = new Font("Segoe UI", 8),
                ForeColor = Color.White,
                Anchor = AnchorStyles.Top | AnchorStyles.Right,
                AutoSize = true
            };

            panel.Controls.AddRange(new Control[]
            {
                _invoiceNumberLabel, _userLabel, _dateTimeLabel, _syncStatusLabel
            });

            // تحديث موقع مؤشر المزامنة
            panel.Resize += (s, e) =>
            {
                _syncStatusLabel.Location = new Point(panel.Width - _syncStatusLabel.Width - 10, 15);
            };

            return panel;
        }

        private Panel CreateMainPanel()
        {
            var panel = new Panel
            {
                Padding = new Padding(5)
            };

            // تقسيم اللوحة إلى جزئين رئيسيين
            var leftPanel = CreateLeftPanel();    // قائمة المنتجات (الشمال)
            var centerPanel = CreateCenterPanel(); // جدول المبيعات (الوسط الكبير)

            panel.Controls.AddRange(new Control[] { leftPanel, centerPanel });

            // ترتيب اللوحات - المنتجات شمال، الجدول يملأ الباقي
            leftPanel.Dock = DockStyle.Left;
            centerPanel.Dock = DockStyle.Fill;

            return panel;
        }

        private Panel CreateLeftPanel()
        {
            var panel = new Panel
            {
                Width = 250, // تصغير عرض لوحة المنتجات
                BackColor = Color.FromArgb(248, 249, 250),
                Padding = new Padding(5)
            };

            // عنوان قائمة المنتجات
            var titleLabel = new Label
            {
                Text = "🛒 المنتجات",
                Font = new Font("Segoe UI", 11, FontStyle.Bold),
                Dock = DockStyle.Top,
                Height = 35,
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White
            };

            // لوحة المنتجات - مربعات
            _productsPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                FlowDirection = FlowDirection.LeftToRight, // ترتيب أفقي
                WrapContents = true, // السماح بالالتفاف
                Padding = new Padding(5)
            };

            panel.Controls.Add(titleLabel);
            panel.Controls.Add(_productsPanel);

            return panel;
        }

        private Panel CreateCenterPanel()
        {
            var panel = new Panel
            {
                BackColor = Color.White,
                Padding = new Padding(5),
                Margin = new Padding(5)
            };

            // لوحة علوية للكود والملخص
            var topPanel = new Panel
            {
                Height = 80,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(248, 249, 250),
                Padding = new Padding(10)
            };

            // حقل الكود
            var codeLabel = new Label
            {
                Text = "كود الصنف:",
                Font = new Font("Segoe UI", 11, FontStyle.Bold),
                Location = new Point(10, 10),
                Size = new Size(80, 25)
            };

            _barcodeTextBox = new TextBox
            {
                Font = new Font("Segoe UI", 14),
                Size = new Size(200, 35),
                Location = new Point(100, 8),
                PlaceholderText = "اكتب كود الصنف هنا..."
            };

            // ملخص سريع
            _totalLabel = new Label
            {
                Text = "الإجمالي: 0.00 ر.س",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.FromArgb(46, 204, 113),
                Location = new Point(350, 10),
                Size = new Size(200, 30)
            };

            // زر الدفع السريع
            var payButton = new Button
            {
                Text = "💳 دفع",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                Size = new Size(100, 35),
                Location = new Point(580, 8),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            payButton.FlatAppearance.BorderSize = 0;
            payButton.Click += async (s, e) => await ProcessPayment();

            topPanel.Controls.AddRange(new Control[] { codeLabel, _barcodeTextBox, _totalLabel, payButton });

            // جدول المبيعات الكبير
            _salesGrid = new DataGridView
            {
                Dock = DockStyle.Fill,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                ReadOnly = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };

            SetupSalesGrid();

            panel.Controls.Add(topPanel);
            panel.Controls.Add(_salesGrid);

            return panel;
        }



        private Panel CreateBottomPanel()
        {
            var panel = new Panel
            {
                Height = 80,
                BackColor = Color.FromArgb(44, 62, 80),
                Padding = new Padding(10)
            };

            // أزرار العمليات
            var saveButton = CreateActionButton("حفظ الفاتورة", Color.FromArgb(46, 204, 113), SaveInvoice_Click);
            var printButton = CreateActionButton("طباعة", Color.FromArgb(52, 152, 219), PrintInvoice_Click);
            var deleteButton = CreateActionButton("حذف صنف", Color.FromArgb(231, 76, 60), DeleteItem_Click);
            var newButton = CreateActionButton("فاتورة جديدة", Color.FromArgb(155, 89, 182), NewInvoice_Click);
            var syncButton = CreateActionButton("مزامنة", Color.FromArgb(243, 156, 18), SyncData_Click);
            var logoutButton = CreateActionButton("تسجيل الخروج", Color.FromArgb(149, 165, 166), Logout_Click);

            // ترتيب الأزرار
            saveButton.Location = new Point(20, 20);
            printButton.Location = new Point(140, 20);
            deleteButton.Location = new Point(260, 20);
            newButton.Location = new Point(380, 20);
            syncButton.Location = new Point(500, 20);
            logoutButton.Location = new Point(panel.Width - 140, 20);
            logoutButton.Anchor = AnchorStyles.Top | AnchorStyles.Right;

            panel.Controls.AddRange(new Control[]
            {
                saveButton, printButton, deleteButton, newButton, syncButton, logoutButton
            });

            return panel;
        }

        private Button CreateActionButton(string text, Color backColor, EventHandler clickHandler)
        {
            var button = new Button
            {
                Text = text,
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                Size = new Size(110, 40),
                BackColor = backColor,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            
            button.FlatAppearance.BorderSize = 0;
            button.Click += clickHandler;
            
            return button;
        }

        private void SetupSalesGrid()
        {
            _salesGrid.Columns.Clear();

            // إعداد الأعمدة مع تحسينات التصميم
            _salesGrid.Columns.Add("ProductCode", "الكود");
            _salesGrid.Columns.Add("ProductName", "اسم المنتج");
            _salesGrid.Columns.Add("Quantity", "الكمية");
            _salesGrid.Columns.Add("UnitPrice", "السعر الوحدة");
            _salesGrid.Columns.Add("TaxRate", "الضريبة %");
            _salesGrid.Columns.Add("Discount", "الخصم");
            _salesGrid.Columns.Add("Total", "المجموع");

            // تخصيص الأعمدة
            _salesGrid.Columns["ProductCode"].Width = 80;
            _salesGrid.Columns["ProductName"].Width = 200;
            _salesGrid.Columns["Quantity"].Width = 80;
            _salesGrid.Columns["UnitPrice"].Width = 100;
            _salesGrid.Columns["TaxRate"].Width = 80;
            _salesGrid.Columns["Discount"].Width = 80;
            _salesGrid.Columns["Total"].Width = 120;

            // جعل أعمدة معينة قابلة للتحرير
            _salesGrid.Columns["ProductCode"].ReadOnly = true;
            _salesGrid.Columns["ProductName"].ReadOnly = true;
            _salesGrid.Columns["UnitPrice"].ReadOnly = true;
            _salesGrid.Columns["TaxRate"].ReadOnly = true;
            _salesGrid.Columns["Total"].ReadOnly = true;

            // تنسيق الأعمدة
            _salesGrid.Columns["Quantity"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            _salesGrid.Columns["UnitPrice"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            _salesGrid.Columns["TaxRate"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            _salesGrid.Columns["Discount"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            _salesGrid.Columns["Total"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;

            // تنسيق الأرقام
            _salesGrid.Columns["UnitPrice"].DefaultCellStyle.Format = "F2";
            _salesGrid.Columns["Discount"].DefaultCellStyle.Format = "F2";
            _salesGrid.Columns["Total"].DefaultCellStyle.Format = "F2";

            // ألوان الصفوف المتناوبة
            _salesGrid.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);
            _salesGrid.DefaultCellStyle.BackColor = Color.White;
            _salesGrid.DefaultCellStyle.SelectionBackColor = Color.FromArgb(52, 152, 219);
            _salesGrid.DefaultCellStyle.SelectionForeColor = Color.White;

            // تنسيق رأس الأعمدة
            _salesGrid.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(44, 62, 80);
            _salesGrid.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            _salesGrid.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10, FontStyle.Bold);
            _salesGrid.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            _salesGrid.ColumnHeadersHeight = 35;

            // إعدادات عامة
            _salesGrid.EnableHeadersVisualStyles = false;
            _salesGrid.GridColor = Color.FromArgb(220, 220, 220);
            _salesGrid.CellBorderStyle = DataGridViewCellBorderStyle.Single;
            _salesGrid.RowHeadersVisible = false;
            _salesGrid.DefaultCellStyle.Font = new Font("Segoe UI", 9);
            _salesGrid.RowTemplate.Height = 35;
        }



        private Label CreateSummaryLabel(string labelText, string valueText, ref int y, int lineHeight, bool isTotal = false, string icon = "")
        {
            // أيقونة
            if (!string.IsNullOrEmpty(icon))
            {
                var iconLabel = new Label
                {
                    Text = icon,
                    Font = new Font("Segoe UI", 14),
                    Location = new Point(10, y),
                    Size = new Size(25, 30),
                    TextAlign = ContentAlignment.MiddleCenter
                };
                _summaryPanel.Controls.Add(iconLabel);
            }

            // تسمية
            var label = new Label
            {
                Text = labelText,
                Font = new Font("Segoe UI", isTotal ? 13 : 10, isTotal ? FontStyle.Bold : FontStyle.Regular),
                Location = new Point(40, y + 2),
                Size = new Size(100, 25),
                TextAlign = ContentAlignment.MiddleLeft,
                ForeColor = isTotal ? Color.FromArgb(46, 204, 113) : Color.FromArgb(44, 62, 80)
            };

            // قيمة
            var valueLabel = new Label
            {
                Name = labelText.Replace(":", "").Replace(" ", "") + "Value",
                Text = valueText,
                Font = new Font("Segoe UI", isTotal ? 14 : 11, isTotal ? FontStyle.Bold : FontStyle.Bold),
                Location = new Point(145, y),
                Size = new Size(85, 30),
                TextAlign = ContentAlignment.MiddleRight,
                ForeColor = isTotal ? Color.FromArgb(46, 204, 113) : Color.FromArgb(52, 73, 94)
            };

            // خلفية للإجمالي
            if (isTotal)
            {
                var background = new Panel
                {
                    Size = new Size(220, 35),
                    Location = new Point(10, y - 5),
                    BackColor = Color.FromArgb(240, 255, 240)
                };
                _summaryPanel.Controls.Add(background);
                background.SendToBack();
            }

            _summaryPanel.Controls.Add(label);
            _summaryPanel.Controls.Add(valueLabel);

            y += lineHeight;

            return isTotal ? valueLabel : label;
        }

        private async void InitializeNewInvoice()
        {
            _currentInvoice = new Invoice
            {
                InvoiceDate = DateTime.Now,
                UserId = _userService.GetCurrentUser()?.Id ?? 0,
                User = _userService.GetCurrentUser(),
                Status = InvoiceStatus.Pending
            };

            // الحصول على رقم فاتورة جديد
            _currentInvoice.InvoiceNumber = await _invoiceService.GetNewInvoiceNumberAsync();
            _invoiceNumberLabel.Text = $"رقم الفاتورة: {_currentInvoice.InvoiceNumber}";

            RefreshSalesGrid();
            UpdateSummary();
        }

        private async void LoadData()
        {
            try
            {
                // تحميل الفئات والمنتجات
                _categories = await _productService.GetCategoriesAsync();
                _products = await _productService.SearchProductsAsync(new ProductSearchRequest { PageSize = 100 });
                
                LoadProductButtons();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadProductButtons()
        {
            _productsPanel.Controls.Clear();
            _productsPanel.FlowDirection = FlowDirection.TopDown;
            _productsPanel.WrapContents = false;

            foreach (var category in _categories)
            {
                // عنوان الفئة مع تصميم محسن
                var categoryPanel = new Panel
                {
                    Size = new Size(280, 35),
                    BackColor = ColorTranslator.FromHtml(category.Color ?? "#3498db"),
                    Margin = new Padding(5, 10, 5, 0)
                };

                var categoryLabel = new Label
                {
                    Text = $"📂 {category.Name}",
                    Font = new Font("Segoe UI", 11, FontStyle.Bold),
                    ForeColor = Color.White,
                    Dock = DockStyle.Fill,
                    TextAlign = ContentAlignment.MiddleCenter
                };

                categoryPanel.Controls.Add(categoryLabel);
                _productsPanel.Controls.Add(categoryPanel);

                // منتجات الفئة
                var categoryProducts = _products.Where(p => p.CategoryId == category.Id && p.IsActive).ToList();

                foreach (var product in categoryProducts)
                {
                    var productPanel = new Panel
                    {
                        Size = new Size(280, 70),
                        BackColor = Color.White,
                        BorderStyle = BorderStyle.FixedSingle,
                        Margin = new Padding(5, 2, 5, 2),
                        Tag = product,
                        Cursor = Cursors.Hand
                    };

                    // اسم المنتج
                    var nameLabel = new Label
                    {
                        Text = product.Name,
                        Font = new Font("Segoe UI", 10, FontStyle.Bold),
                        ForeColor = Color.FromArgb(44, 62, 80),
                        Location = new Point(10, 8),
                        Size = new Size(200, 20),
                        TextAlign = ContentAlignment.MiddleLeft
                    };

                    // السعر
                    var priceLabel = new Label
                    {
                        Text = $"{product.Price:F2} ر.س",
                        Font = new Font("Segoe UI", 12, FontStyle.Bold),
                        ForeColor = Color.FromArgb(46, 204, 113),
                        Location = new Point(10, 30),
                        Size = new Size(120, 25),
                        TextAlign = ContentAlignment.MiddleLeft
                    };

                    // معلومات الضريبة
                    var taxInfo = "";
                    if (product.TaxRate > 0)
                    {
                        taxInfo = product.IsTaxInclusive ? $"شامل {product.TaxRate}%" : $"+ {product.TaxRate}%";
                    }
                    else
                    {
                        taxInfo = "معفى";
                    }

                    var taxLabel = new Label
                    {
                        Text = taxInfo,
                        Font = new Font("Segoe UI", 8),
                        ForeColor = Color.FromArgb(149, 165, 166),
                        Location = new Point(140, 35),
                        Size = new Size(60, 20),
                        TextAlign = ContentAlignment.MiddleLeft
                    };

                    // كود المنتج
                    var codeLabel = new Label
                    {
                        Text = $"#{product.Code}",
                        Font = new Font("Segoe UI", 8),
                        ForeColor = Color.FromArgb(149, 165, 166),
                        Location = new Point(210, 8),
                        Size = new Size(60, 20),
                        TextAlign = ContentAlignment.MiddleRight
                    };

                    // أيقونة المنتج
                    var iconLabel = new Label
                    {
                        Text = "🛒",
                        Font = new Font("Segoe UI", 16),
                        Location = new Point(240, 25),
                        Size = new Size(30, 30),
                        TextAlign = ContentAlignment.MiddleCenter
                    };

                    productPanel.Controls.AddRange(new Control[]
                    {
                        nameLabel, priceLabel, taxLabel, codeLabel, iconLabel
                    });

                    // تأثير الهوفر
                    productPanel.MouseEnter += (s, e) =>
                    {
                        productPanel.BackColor = Color.FromArgb(240, 248, 255);
                    };

                    productPanel.MouseLeave += (s, e) =>
                    {
                        productPanel.BackColor = Color.White;
                    };

                    // معالج النقر
                    productPanel.Click += ProductButton_Click;
                    foreach (Control control in productPanel.Controls)
                    {
                        control.Click += ProductButton_Click;
                        control.Tag = product;
                    }

                    _productsPanel.Controls.Add(productPanel);
                }

                // خط فاصل بين الفئات
                if (category != _categories.Last())
                {
                    var separator = new Panel
                    {
                        Size = new Size(280, 2),
                        BackColor = Color.FromArgb(220, 220, 220),
                        Margin = new Padding(5, 5, 5, 5)
                    };
                    _productsPanel.Controls.Add(separator);
                }
            }
        }

        private void SetupEventHandlers()
        {
            // معالج الباركود
            _barcodeTextBox.KeyPress += BarcodeTextBox_KeyPress;
            
            // معالج تحرير الجدول
            _salesGrid.CellValueChanged += SalesGrid_CellValueChanged;
            _salesGrid.KeyDown += SalesGrid_KeyDown;
            
            // تحديث الوقت وحالة المزامنة
            var timer = new System.Windows.Forms.Timer { Interval = 1000 };
            timer.Tick += async (s, e) =>
            {
                _dateTimeLabel.Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm");
                await UpdateSyncStatus();
            };
            timer.Start();
        }

        // Event Handlers
        private async void BarcodeTextBox_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                var barcode = _barcodeTextBox.Text.Trim();
                if (!string.IsNullOrEmpty(barcode))
                {
                    await AddProductByBarcode(barcode);
                    _barcodeTextBox.Clear();
                }
                e.Handled = true;
            }
        }

        private void ProductButton_Click(object sender, EventArgs e)
        {
            if (sender is Button button && button.Tag is Product product)
            {
                AddProductToInvoice(product, 1);
            }
        }

        private void SalesGrid_CellValueChanged(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0 && e.ColumnIndex >= 0)
            {
                var columnName = _salesGrid.Columns[e.ColumnIndex].Name;

                if (columnName == "Quantity" || columnName == "UnitPrice" || columnName == "Discount")
                {
                    UpdateInvoiceItemFromGrid(e.RowIndex);
                }
            }
        }

        private void SalesGrid_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Delete && _salesGrid.SelectedRows.Count > 0)
            {
                DeleteSelectedItem();
            }
        }

        private async void SaveInvoice_Click(object sender, EventArgs e)
        {
            await SaveCurrentInvoice();
        }

        private async void PrintInvoice_Click(object sender, EventArgs e)
        {
            await PrintCurrentInvoice();
        }

        private void DeleteItem_Click(object sender, EventArgs e)
        {
            DeleteSelectedItem();
        }

        private void NewInvoice_Click(object sender, EventArgs e)
        {
            CreateNewInvoice();
        }

        private async void Logout_Click(object sender, EventArgs e)
        {
            await LogoutUser();
        }

        private async void SyncData_Click(object sender, EventArgs e)
        {
            await SyncOfflineData();
        }

        private void RefreshSalesGrid()
        {
            _salesGrid.Rows.Clear();

            foreach (var item in _currentInvoice.Items)
            {
                var row = new DataGridViewRow();
                row.CreateCells(_salesGrid);

                row.Cells[0].Value = item.ProductCode ?? ""; // الكود
                row.Cells[1].Value = item.ProductName; // اسم المنتج
                row.Cells[2].Value = item.Quantity; // الكمية
                row.Cells[3].Value = item.UnitPrice; // السعر الوحدة
                row.Cells[4].Value = $"{item.TaxRate:F0}%"; // الضريبة %
                row.Cells[5].Value = item.DiscountAmount; // الخصم
                row.Cells[6].Value = item.Total; // المجموع
                row.Tag = item;

                // تلوين الصف حسب حالة الضريبة
                if (item.TaxRate > 0)
                {
                    if (item.IsTaxInclusive)
                    {
                        row.DefaultCellStyle.BackColor = Color.FromArgb(250, 255, 250); // أخضر فاتح للضريبة الشاملة
                    }
                    else
                    {
                        row.DefaultCellStyle.BackColor = Color.FromArgb(255, 250, 250); // أحمر فاتح للضريبة غير الشاملة
                    }
                }

                _salesGrid.Rows.Add(row);
            }
        }

        private void UpdateSummary()
        {
            _currentInvoice.CalculateTotal();

            var subTotalLabel = _summaryPanel.Controls.Find("المجموعالفرعيValue", true).FirstOrDefault() as Label;
            var discountLabel = _summaryPanel.Controls.Find("الخصمValue", true).FirstOrDefault() as Label;
            var taxLabel = _summaryPanel.Controls.Find("الضريبةValue", true).FirstOrDefault() as Label;
            var totalLabel = _summaryPanel.Controls.Find("الإجماليValue", true).FirstOrDefault() as Label;
            var paidLabel = _summaryPanel.Controls.Find("المدفوعValue", true).FirstOrDefault() as Label;
            var changeLabel = _summaryPanel.Controls.Find("الباقيValue", true).FirstOrDefault() as Label;

            if (subTotalLabel != null) subTotalLabel.Text = $"{_currentInvoice.SubTotal:F2} ر.س";
            if (discountLabel != null) discountLabel.Text = $"{_currentInvoice.DiscountAmount:F2} ر.س";
            if (taxLabel != null) taxLabel.Text = $"{_currentInvoice.TaxAmount:F2} ر.س";
            if (totalLabel != null) totalLabel.Text = $"{_currentInvoice.Total:F2} ر.س";
            if (paidLabel != null) paidLabel.Text = $"{_currentInvoice.PaidAmount:F2} ر.س";
            if (changeLabel != null) changeLabel.Text = $"{_currentInvoice.ChangeAmount:F2} ر.س";
        }

        // Helper Methods
        private async Task AddProductByBarcode(string barcode)
        {
            try
            {
                Product? product = null;

                // البحث بالباركود أولاً
                if (barcode.Length > 5)
                {
                    product = await _productService.GetProductByBarcodeAsync(barcode);
                }

                // إذا لم يوجد، البحث بالكود
                if (product == null)
                {
                    product = await _productService.GetProductByCodeAsync(barcode);
                }

                if (product != null)
                {
                    AddProductToInvoice(product, 1);
                }
                else
                {
                    MessageBox.Show("لم يتم العثور على المنتج", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث عن المنتج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void AddProductToInvoice(Product product, decimal quantity)
        {
            try
            {
                // التحقق من الصلاحيات إذا كان السعر مختلف
                var currentUser = _userService.GetCurrentUser();

                _currentInvoice.AddItem(product, quantity);
                RefreshSalesGrid();
                UpdateSummary();

                // تركيز على حقل الباركود
                _barcodeTextBox.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة المنتج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateInvoiceItemFromGrid(int rowIndex)
        {
            try
            {
                if (rowIndex >= _currentInvoice.Items.Count) return;

                var item = _currentInvoice.Items[rowIndex];
                var row = _salesGrid.Rows[rowIndex];

                // تحديث الكمية (العمود 2)
                if (decimal.TryParse(row.Cells[2].Value?.ToString(), out decimal quantity))
                {
                    item.Quantity = quantity;
                }

                // تحديث السعر (العمود 3) - مع التحقق من الصلاحيات
                if (decimal.TryParse(row.Cells[3].Value?.ToString(), out decimal unitPrice))
                {
                    if (unitPrice != item.UnitPrice)
                    {
                        if (!_userService.HasPermission("editprices"))
                        {
                            MessageBox.Show("ليس لديك صلاحية لتعديل الأسعار", "تنبيه",
                                MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            row.Cells[3].Value = item.UnitPrice;
                            return;
                        }
                    }
                    item.UnitPrice = unitPrice;
                }

                // تحديث الخصم (العمود 5) - مع التحقق من الصلاحيات
                if (decimal.TryParse(row.Cells[5].Value?.ToString(), out decimal discount))
                {
                    if (discount > 0 && !_userService.HasPermission("applydiscounts"))
                    {
                        MessageBox.Show("ليس لديك صلاحية لتطبيق الخصومات", "تنبيه",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        row.Cells[5].Value = 0;
                        return;
                    }
                    item.DiscountAmount = discount;
                }

                item.CalculateTotal();
                row.Cells[6].Value = item.Total; // العمود 6 للمجموع

                UpdateSummary();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث العنصر: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void DeleteSelectedItem()
        {
            try
            {
                if (_salesGrid.SelectedRows.Count == 0)
                {
                    MessageBox.Show("يرجى اختيار عنصر للحذف", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (!_userService.HasPermission("deleteitems"))
                {
                    MessageBox.Show("ليس لديك صلاحية لحذف العناصر", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var result = MessageBox.Show("هل أنت متأكد من حذف هذا العنصر؟", "تأكيد الحذف",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    var rowIndex = _salesGrid.SelectedRows[0].Index;
                    if (rowIndex < _currentInvoice.Items.Count)
                    {
                        _currentInvoice.Items.RemoveAt(rowIndex);
                        RefreshSalesGrid();
                        UpdateSummary();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف العنصر: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task SaveCurrentInvoice()
        {
            try
            {
                if (_currentInvoice.Items.Count == 0)
                {
                    MessageBox.Show("لا يمكن حفظ فاتورة فارغة", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // طلب المبلغ المدفوع
                var paymentForm = new PaymentForm(_currentInvoice.Total);
                if (paymentForm.ShowDialog() == DialogResult.OK)
                {
                    _currentInvoice.PaidAmount = paymentForm.PaidAmount;
                    _currentInvoice.PaymentMethod = paymentForm.PaymentMethod;
                    _currentInvoice.Status = InvoiceStatus.Completed;

                    var success = await _invoiceService.SaveInvoiceAsync(_currentInvoice);

                    if (success)
                    {
                        MessageBox.Show("تم حفظ الفاتورة بنجاح", "نجح",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);

                        UpdateSummary();

                        // سؤال عن الطباعة
                        var printResult = MessageBox.Show("هل تريد طباعة الفاتورة؟", "طباعة",
                            MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                        if (printResult == DialogResult.Yes)
                        {
                            await PrintCurrentInvoice();
                        }
                    }
                    else
                    {
                        MessageBox.Show("فشل في حفظ الفاتورة", "خطأ",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الفاتورة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task PrintCurrentInvoice()
        {
            try
            {
                if (_currentInvoice.Items.Count == 0)
                {
                    MessageBox.Show("لا يمكن طباعة فاتورة فارغة", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var success = await _invoiceService.PrintInvoiceAsync(_currentInvoice);

                if (success)
                {
                    MessageBox.Show("تم طباعة الفاتورة بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("فشل في طباعة الفاتورة", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة الفاتورة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CreateNewInvoice()
        {
            try
            {
                if (_currentInvoice.Items.Count > 0)
                {
                    var result = MessageBox.Show("هناك فاتورة حالية غير محفوظة. هل تريد المتابعة؟",
                        "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                    if (result != DialogResult.Yes)
                        return;
                }

                InitializeNewInvoice();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء فاتورة جديدة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task LogoutUser()
        {
            try
            {
                var result = MessageBox.Show("هل أنت متأكد من تسجيل الخروج؟", "تأكيد",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    await _userService.LogoutAsync();
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تسجيل الخروج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task UpdateSyncStatus()
        {
            try
            {
                var isOnline = await _offlineDataService.IsOnlineAsync();
                var pendingCount = await _offlineDataService.GetPendingInvoicesCountAsync();

                if (isOnline)
                {
                    if (pendingCount > 0)
                    {
                        _syncStatusLabel.Text = $"🔄 مزامنة ({pendingCount} معلق)";
                        _syncStatusLabel.ForeColor = Color.Orange;

                        // محاولة مزامنة تلقائية
                        _ = Task.Run(async () => await _offlineDataService.SyncOfflineDataAsync());
                    }
                    else
                    {
                        _syncStatusLabel.Text = "✅ متصل";
                        _syncStatusLabel.ForeColor = Color.LightGreen;
                    }
                }
                else
                {
                    if (pendingCount > 0)
                    {
                        _syncStatusLabel.Text = $"❌ غير متصل ({pendingCount} معلق)";
                        _syncStatusLabel.ForeColor = Color.Red;
                    }
                    else
                    {
                        _syncStatusLabel.Text = "❌ غير متصل";
                        _syncStatusLabel.ForeColor = Color.Red;
                    }
                }
            }
            catch (Exception)
            {
                _syncStatusLabel.Text = "⚠️ خطأ في الحالة";
                _syncStatusLabel.ForeColor = Color.Yellow;
            }
        }

        private async Task ProcessPayment()
        {
            try
            {
                if (_currentInvoice.Items.Count == 0)
                {
                    MessageBox.Show("لا يمكن إتمام الدفع لفاتورة فارغة", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // طلب المبلغ المدفوع
                var paymentForm = new PaymentForm(_currentInvoice.Total);
                if (paymentForm.ShowDialog() == DialogResult.OK)
                {
                    _currentInvoice.PaidAmount = paymentForm.PaidAmount;
                    _currentInvoice.PaymentMethod = paymentForm.PaymentMethod;
                    _currentInvoice.Status = InvoiceStatus.Completed;

                    var success = await _invoiceService.SaveInvoiceAsync(_currentInvoice);

                    if (success)
                    {
                        MessageBox.Show("تم إتمام الدفع وحفظ الفاتورة بنجاح", "نجح",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);

                        UpdateSummary();

                        // سؤال عن الطباعة
                        var printResult = MessageBox.Show("هل تريد طباعة الفاتورة؟", "طباعة",
                            MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                        if (printResult == DialogResult.Yes)
                        {
                            await PrintCurrentInvoice();
                        }

                        // إنشاء فاتورة جديدة
                        var newInvoiceResult = MessageBox.Show("هل تريد إنشاء فاتورة جديدة؟", "فاتورة جديدة",
                            MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                        if (newInvoiceResult == DialogResult.Yes)
                        {
                            CreateNewInvoice();
                        }
                    }
                    else
                    {
                        MessageBox.Show("فشل في حفظ الفاتورة", "خطأ",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إتمام الدفع: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task SyncOfflineData()
        {
            try
            {
                var pendingCount = await _offlineDataService.GetPendingInvoicesCountAsync();

                if (pendingCount == 0)
                {
                    MessageBox.Show("لا توجد بيانات معلقة للمزامنة", "معلومات",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                var result = MessageBox.Show($"سيتم مزامنة {pendingCount} فاتورة معلقة. هل تريد المتابعة؟",
                    "تأكيد المزامنة", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result != DialogResult.Yes) return;

                // تعطيل الواجهة أثناء المزامنة
                this.Enabled = false;
                _syncStatusLabel.Text = "🔄 جاري المزامنة...";
                _syncStatusLabel.ForeColor = Color.Orange;

                var success = await _offlineDataService.SyncOfflineDataAsync();

                if (success)
                {
                    MessageBox.Show("تم إرسال جميع البيانات بنجاح", "نجحت المزامنة",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    var remainingCount = await _offlineDataService.GetPendingInvoicesCountAsync();
                    MessageBox.Show($"تم إرسال بعض البيانات. يتبقى {remainingCount} فاتورة",
                        "مزامنة جزئية", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في المزامنة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                this.Enabled = true;
                await UpdateSyncStatus();
            }
        }
    }
}
