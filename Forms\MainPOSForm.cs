using POSSystem.Models;
using POSSystem.Services;
using System.Data;

namespace POSSystem.Forms
{
    public partial class MainPOSForm : Form
    {
        private readonly IUserService _userService;
        private readonly IProductService _productService;
        private readonly IInvoiceService _invoiceService;
        private readonly IPrintService _printService;
        private readonly IOfflineDataService _offlineDataService;

        private Invoice _currentInvoice;
        private List<Product> _products = new();
        private List<Category> _categories = new();
        
        // Controls
        private DataGridView _salesGrid;
        private FlowLayoutPanel _productsPanel;

        private TextBox _barcodeTextBox;
        private Label _totalLabel;
        private Label _invoiceNumberLabel;
        private Label _userLabel;
        private Label _dateTimeLabel;
        private Label _syncStatusLabel;

        public MainPOSForm(IUserService userService, IProductService productService,
            IInvoiceService invoiceService, IPrintService printService, IOfflineDataService offlineDataService)
        {
            _userService = userService;
            _productService = productService;
            _invoiceService = invoiceService;
            _printService = printService;
            _offlineDataService = offlineDataService;
            
            InitializeComponent();
            SetupForm();
            InitializeNewInvoice();
            LoadData();
        }

        private void SetupForm()
        {
            // إعداد النموذج الرئيسي
            this.Text = "نظام نقطة البيع - الشاشة الرئيسية";
            this.WindowState = FormWindowState.Maximized;
            this.BackColor = Color.FromArgb(245, 245, 245);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.KeyPreview = true;

            CreateLayout();
            SetupEventHandlers();
        }

        private void CreateLayout()
        {
            // لوحة علوية - معلومات الفاتورة
            var topPanel = CreateTopPanel();
            
            // لوحة وسطى - المحتوى الرئيسي
            var mainPanel = CreateMainPanel();
            
            // لوحة سفلية - أزرار العمليات
            var bottomPanel = CreateBottomPanel();

            // إضافة اللوحات للنموذج
            this.Controls.Add(topPanel);
            this.Controls.Add(mainPanel);
            this.Controls.Add(bottomPanel);

            // ترتيب اللوحات
            topPanel.Dock = DockStyle.Top;
            bottomPanel.Dock = DockStyle.Bottom;
            mainPanel.Dock = DockStyle.Fill;
        }

        private Panel CreateTopPanel()
        {
            var panel = new Panel
            {
                Height = 50, // تصغير الهيدر
                BackColor = Color.FromArgb(52, 152, 219),
                Padding = new Padding(5)
            };

            // معلومات الفاتورة مضغوطة
            _invoiceNumberLabel = new Label
            {
                Text = "فاتورة: جاري التحميل...",
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(10, 15),
                AutoSize = true
            };

            _userLabel = new Label
            {
                Text = $"المستخدم: {_userService.GetCurrentUser()?.FullName ?? "غير محدد"}",
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.White,
                Location = new Point(200, 15),
                AutoSize = true
            };

            _dateTimeLabel = new Label
            {
                Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm"),
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.White,
                Location = new Point(400, 15),
                AutoSize = true
            };

            // مؤشر حالة المزامنة
            _syncStatusLabel = new Label
            {
                Text = "🔄 جاري التحقق...",
                Font = new Font("Segoe UI", 8),
                ForeColor = Color.White,
                Anchor = AnchorStyles.Top | AnchorStyles.Right,
                AutoSize = true
            };

            panel.Controls.AddRange(new Control[]
            {
                _invoiceNumberLabel, _userLabel, _dateTimeLabel, _syncStatusLabel
            });

            // تحديث موقع مؤشر المزامنة
            panel.Resize += (s, e) =>
            {
                _syncStatusLabel.Location = new Point(panel.Width - _syncStatusLabel.Width - 10, 15);
            };

            return panel;
        }

        private Panel CreateMainPanel()
        {
            var panel = new Panel
            {
                Padding = new Padding(5)
            };

            // تقسيم اللوحة إلى جزئين رئيسيين
            var rightPanel = CreateRightPanel();   // قائمة المنتجات (اليمين)
            var centerPanel = CreateCenterPanel(); // جدول المبيعات (الوسط الكبير)

            panel.Controls.AddRange(new Control[] { rightPanel, centerPanel });

            // ترتيب اللوحات - المنتجات يمين، الجدول يملأ الباقي
            rightPanel.Dock = DockStyle.Right;
            centerPanel.Dock = DockStyle.Fill;

            return panel;
        }

        private Panel CreateRightPanel()
        {
            var panel = new Panel
            {
                Width = 300, // عرض أكبر للمنتجات على شكل مربعات
                BackColor = Color.FromArgb(248, 249, 250),
                Padding = new Padding(5)
            };

            // عنوان قائمة المنتجات
            var titleLabel = new Label
            {
                Text = "🛒 الأصناف",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                Dock = DockStyle.Top,
                Height = 40,
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White
            };

            // لوحة المنتجات - مربعات منظمة
            _productsPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                FlowDirection = FlowDirection.LeftToRight, // ترتيب أفقي
                WrapContents = true, // السماح بالالتفاف
                Padding = new Padding(8),
                BackColor = Color.White
            };

            panel.Controls.Add(titleLabel);
            panel.Controls.Add(_productsPanel);

            return panel;
        }

        private Panel CreateCenterPanel()
        {
            var panel = new Panel
            {
                BackColor = Color.White,
                Padding = new Padding(5),
                Margin = new Padding(5)
            };

            // لوحة علوية للكود والملخص
            var topPanel = new Panel
            {
                Height = 60,
                Dock = DockStyle.Top,
                BackColor = Color.FromArgb(248, 249, 250),
                Padding = new Padding(10)
            };

            // حقل الكود
            var codeLabel = new Label
            {
                Text = "كود الصنف:",
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                Location = new Point(10, 8),
                Size = new Size(80, 25)
            };

            _barcodeTextBox = new TextBox
            {
                Font = new Font("Segoe UI", 12),
                Size = new Size(180, 30),
                Location = new Point(100, 5),
                PlaceholderText = "اكتب الكود واضغط Enter"
            };

            // ملخص سريع في نفس الصف
            var subtotalLabel = new Label
            {
                Text = "المجموع الفرعي:",
                Font = new Font("Segoe UI", 9),
                Location = new Point(300, 8),
                Size = new Size(80, 20)
            };

            var taxLabel = new Label
            {
                Text = "الضريبة:",
                Font = new Font("Segoe UI", 9),
                Location = new Point(300, 28),
                Size = new Size(50, 20)
            };

            _totalLabel = new Label
            {
                Text = "الإجمالي: 0.00 ر.س",
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(46, 204, 113),
                Location = new Point(450, 8),
                Size = new Size(200, 30)
            };

            topPanel.Controls.AddRange(new Control[] {
                codeLabel, _barcodeTextBox, subtotalLabel, taxLabel, _totalLabel
            });

            // جدول المبيعات الكبير
            _salesGrid = new DataGridView
            {
                Dock = DockStyle.Fill,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                ReadOnly = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };

            SetupSalesGrid();

            panel.Controls.Add(topPanel);
            panel.Controls.Add(_salesGrid);

            return panel;
        }



        private Panel CreateBottomPanel()
        {
            var panel = new Panel
            {
                Height = 70,
                BackColor = Color.FromArgb(248, 249, 250),
                Padding = new Padding(10),
                BorderStyle = BorderStyle.FixedSingle
            };

            // أزرار العمليات مع أيقونات
            var saveButton = CreateActionButton("💾 حفظ", Color.FromArgb(46, 204, 113), SaveInvoice_Click);
            var printButton = CreateActionButton("🖨️ طباعة", Color.FromArgb(52, 152, 219), PrintInvoice_Click);
            var payButton = CreateActionButton("💳 دفع", Color.FromArgb(46, 204, 113), async (s, e) => await ProcessPayment());
            var deleteButton = CreateActionButton("🗑️ حذف", Color.FromArgb(231, 76, 60), DeleteItem_Click);
            var newButton = CreateActionButton("📄 جديد", Color.FromArgb(155, 89, 182), NewInvoice_Click);
            var syncButton = CreateActionButton("🔄 مزامنة", Color.FromArgb(243, 156, 18), SyncData_Click);
            var logoutButton = CreateActionButton("🚪 خروج", Color.FromArgb(149, 165, 166), Logout_Click);

            // ترتيب الأزرار في صف واحد
            var buttonSpacing = 100;
            var startX = 20;

            saveButton.Location = new Point(startX, 15);
            printButton.Location = new Point(startX + buttonSpacing, 15);
            payButton.Location = new Point(startX + buttonSpacing * 2, 15);
            deleteButton.Location = new Point(startX + buttonSpacing * 3, 15);
            newButton.Location = new Point(startX + buttonSpacing * 4, 15);
            syncButton.Location = new Point(startX + buttonSpacing * 5, 15);

            // زر الخروج في أقصى اليمين
            logoutButton.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            logoutButton.Location = new Point(panel.Width - 110, 15);

            panel.Controls.AddRange(new Control[]
            {
                saveButton, printButton, payButton, deleteButton, newButton, syncButton, logoutButton
            });

            return panel;
        }

        private Button CreateActionButton(string text, Color backColor, EventHandler clickHandler)
        {
            var button = new Button
            {
                Text = text,
                Font = new Font("Segoe UI", 9, FontStyle.Bold),
                Size = new Size(90, 40),
                BackColor = backColor,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand,
                TextAlign = ContentAlignment.MiddleCenter
            };

            button.FlatAppearance.BorderSize = 0;

            // تأثيرات الهوفر
            button.MouseEnter += (s, e) =>
            {
                button.BackColor = ControlPaint.Light(backColor, 0.2f);
            };

            button.MouseLeave += (s, e) =>
            {
                button.BackColor = backColor;
            };

            button.Click += clickHandler;

            return button;
        }

        private void SetupSalesGrid()
        {
            _salesGrid.Columns.Clear();

            // إعداد الأعمدة مع تحسينات التصميم
            _salesGrid.Columns.Add("ProductCode", "الكود");
            _salesGrid.Columns.Add("ProductName", "اسم المنتج");
            _salesGrid.Columns.Add("Quantity", "الكمية");
            _salesGrid.Columns.Add("UnitPrice", "السعر الوحدة");
            _salesGrid.Columns.Add("TaxRate", "الضريبة %");
            _salesGrid.Columns.Add("Discount", "الخصم");
            _salesGrid.Columns.Add("Total", "المجموع");

            // تخصيص الأعمدة
            _salesGrid.Columns["ProductCode"].Width = 80;
            _salesGrid.Columns["ProductName"].Width = 200;
            _salesGrid.Columns["Quantity"].Width = 80;
            _salesGrid.Columns["UnitPrice"].Width = 100;
            _salesGrid.Columns["TaxRate"].Width = 80;
            _salesGrid.Columns["Discount"].Width = 80;
            _salesGrid.Columns["Total"].Width = 120;

            // جعل أعمدة معينة قابلة للتحرير
            _salesGrid.Columns["ProductCode"].ReadOnly = true;
            _salesGrid.Columns["ProductName"].ReadOnly = true;
            _salesGrid.Columns["UnitPrice"].ReadOnly = true;
            _salesGrid.Columns["TaxRate"].ReadOnly = true;
            _salesGrid.Columns["Total"].ReadOnly = true;

            // تنسيق الأعمدة
            _salesGrid.Columns["Quantity"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            _salesGrid.Columns["UnitPrice"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            _salesGrid.Columns["TaxRate"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            _salesGrid.Columns["Discount"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            _salesGrid.Columns["Total"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;

            // تنسيق الأرقام
            _salesGrid.Columns["UnitPrice"].DefaultCellStyle.Format = "F2";
            _salesGrid.Columns["Discount"].DefaultCellStyle.Format = "F2";
            _salesGrid.Columns["Total"].DefaultCellStyle.Format = "F2";

            // ألوان الصفوف المتناوبة
            _salesGrid.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);
            _salesGrid.DefaultCellStyle.BackColor = Color.White;
            _salesGrid.DefaultCellStyle.SelectionBackColor = Color.FromArgb(52, 152, 219);
            _salesGrid.DefaultCellStyle.SelectionForeColor = Color.White;

            // تنسيق رأس الأعمدة
            _salesGrid.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(44, 62, 80);
            _salesGrid.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            _salesGrid.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 10, FontStyle.Bold);
            _salesGrid.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            _salesGrid.ColumnHeadersHeight = 35;

            // إعدادات عامة
            _salesGrid.EnableHeadersVisualStyles = false;
            _salesGrid.GridColor = Color.FromArgb(220, 220, 220);
            _salesGrid.CellBorderStyle = DataGridViewCellBorderStyle.Single;
            _salesGrid.RowHeadersVisible = false;
            _salesGrid.DefaultCellStyle.Font = new Font("Segoe UI", 9);
            _salesGrid.RowTemplate.Height = 35;
        }



        private Label CreateSummaryLabel(string labelText, string valueText, ref int y, int lineHeight, bool isTotal = false, string icon = "")
        {
            // أيقونة
            if (!string.IsNullOrEmpty(icon))
            {
                var iconLabel = new Label
                {
                    Text = icon,
                    Font = new Font("Segoe UI", 14),
                    Location = new Point(10, y),
                    Size = new Size(25, 30),
                    TextAlign = ContentAlignment.MiddleCenter
                };
                _summaryPanel.Controls.Add(iconLabel);
            }

            // تسمية
            var label = new Label
            {
                Text = labelText,
                Font = new Font("Segoe UI", isTotal ? 13 : 10, isTotal ? FontStyle.Bold : FontStyle.Regular),
                Location = new Point(40, y + 2),
                Size = new Size(100, 25),
                TextAlign = ContentAlignment.MiddleLeft,
                ForeColor = isTotal ? Color.FromArgb(46, 204, 113) : Color.FromArgb(44, 62, 80)
            };

            // قيمة
            var valueLabel = new Label
            {
                Name = labelText.Replace(":", "").Replace(" ", "") + "Value",
                Text = valueText,
                Font = new Font("Segoe UI", isTotal ? 14 : 11, isTotal ? FontStyle.Bold : FontStyle.Bold),
                Location = new Point(145, y),
                Size = new Size(85, 30),
                TextAlign = ContentAlignment.MiddleRight,
                ForeColor = isTotal ? Color.FromArgb(46, 204, 113) : Color.FromArgb(52, 73, 94)
            };

            // خلفية للإجمالي
            if (isTotal)
            {
                var background = new Panel
                {
                    Size = new Size(220, 35),
                    Location = new Point(10, y - 5),
                    BackColor = Color.FromArgb(240, 255, 240)
                };
                _summaryPanel.Controls.Add(background);
                background.SendToBack();
            }

            _summaryPanel.Controls.Add(label);
            _summaryPanel.Controls.Add(valueLabel);

            y += lineHeight;

            return isTotal ? valueLabel : label;
        }

        private async void InitializeNewInvoice()
        {
            _currentInvoice = new Invoice
            {
                InvoiceDate = DateTime.Now,
                UserId = _userService.GetCurrentUser()?.Id ?? 0,
                User = _userService.GetCurrentUser(),
                Status = InvoiceStatus.Pending
            };

            // الحصول على رقم فاتورة جديد
            _currentInvoice.InvoiceNumber = await _invoiceService.GetNewInvoiceNumberAsync();
            _invoiceNumberLabel.Text = $"رقم الفاتورة: {_currentInvoice.InvoiceNumber}";

            RefreshSalesGrid();
            UpdateSummary();
        }

        private async void LoadData()
        {
            try
            {
                // تحميل الفئات والمنتجات
                _categories = await _productService.GetCategoriesAsync();
                _products = await _productService.SearchProductsAsync(new ProductSearchRequest { PageSize = 100 });
                
                LoadProductButtons();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadProductButtons()
        {
            _productsPanel.Controls.Clear();

            // إضافة جميع المنتجات على شكل مربعات صغيرة
            var allProducts = _products.Where(p => p.IsActive).ToList();

            foreach (var product in allProducts)
            {
                // مربع المنتج
                var productButton = new Button
                {
                    Size = new Size(85, 85), // مربع صغير مثل الصورة
                    BackColor = Color.White,
                    FlatStyle = FlatStyle.Flat,
                    Margin = new Padding(3),
                    Tag = product,
                    Cursor = Cursors.Hand,
                    TextAlign = ContentAlignment.BottomCenter,
                    Font = new Font("Segoe UI", 8, FontStyle.Bold),
                    ForeColor = Color.FromArgb(44, 62, 80)
                };

                // تحديد لون الحدود حسب الفئة
                var category = _categories.FirstOrDefault(c => c.Id == product.CategoryId);
                var borderColor = category != null ? ColorTranslator.FromHtml(category.Color ?? "#3498db") : Color.FromArgb(52, 152, 219);

                productButton.FlatAppearance.BorderColor = borderColor;
                productButton.FlatAppearance.BorderSize = 2;

                // النص: اسم المنتج والسعر
                productButton.Text = $"{product.Name}\n{product.Price:F2} ر.س";

                // تأثيرات الهوفر
                productButton.MouseEnter += (s, e) =>
                {
                    productButton.BackColor = Color.FromArgb(240, 248, 255);
                    productButton.FlatAppearance.BorderSize = 3;
                };

                productButton.MouseLeave += (s, e) =>
                {
                    productButton.BackColor = Color.White;
                    productButton.FlatAppearance.BorderSize = 2;
                };

                // معالج النقر
                productButton.Click += ProductButton_Click;

                _productsPanel.Controls.Add(productButton);
            }
        }

        private void SetupEventHandlers()
        {
            // معالج الباركود
            _barcodeTextBox.KeyPress += BarcodeTextBox_KeyPress;
            
            // معالج تحرير الجدول
            _salesGrid.CellValueChanged += SalesGrid_CellValueChanged;
            _salesGrid.KeyDown += SalesGrid_KeyDown;
            
            // تحديث الوقت وحالة المزامنة
            var timer = new System.Windows.Forms.Timer { Interval = 1000 };
            timer.Tick += async (s, e) =>
            {
                _dateTimeLabel.Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm");
                await UpdateSyncStatus();
            };
            timer.Start();
        }

        // Event Handlers
        private async void BarcodeTextBox_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                var barcode = _barcodeTextBox.Text.Trim();
                if (!string.IsNullOrEmpty(barcode))
                {
                    await AddProductByBarcode(barcode);
                    _barcodeTextBox.Clear();
                }
                e.Handled = true;
            }
        }

        private void ProductButton_Click(object sender, EventArgs e)
        {
            if (sender is Button button && button.Tag is Product product)
            {
                AddProductToInvoice(product, 1);
            }
        }

        private void SalesGrid_CellValueChanged(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0 && e.ColumnIndex >= 0)
            {
                var columnName = _salesGrid.Columns[e.ColumnIndex].Name;

                if (columnName == "Quantity" || columnName == "UnitPrice" || columnName == "Discount")
                {
                    UpdateInvoiceItemFromGrid(e.RowIndex);
                }
            }
        }

        private void SalesGrid_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Delete && _salesGrid.SelectedRows.Count > 0)
            {
                DeleteSelectedItem();
            }
        }

        private async void SaveInvoice_Click(object sender, EventArgs e)
        {
            await SaveCurrentInvoice();
        }

        private async void PrintInvoice_Click(object sender, EventArgs e)
        {
            await PrintCurrentInvoice();
        }

        private void DeleteItem_Click(object sender, EventArgs e)
        {
            DeleteSelectedItem();
        }

        private void NewInvoice_Click(object sender, EventArgs e)
        {
            CreateNewInvoice();
        }

        private async void Logout_Click(object sender, EventArgs e)
        {
            await LogoutUser();
        }

        private async void SyncData_Click(object sender, EventArgs e)
        {
            await SyncOfflineData();
        }

        private void RefreshSalesGrid()
        {
            _salesGrid.Rows.Clear();

            foreach (var item in _currentInvoice.Items)
            {
                var row = new DataGridViewRow();
                row.CreateCells(_salesGrid);

                row.Cells[0].Value = item.ProductCode ?? ""; // الكود
                row.Cells[1].Value = item.ProductName; // اسم المنتج
                row.Cells[2].Value = item.Quantity; // الكمية
                row.Cells[3].Value = item.UnitPrice; // السعر الوحدة
                row.Cells[4].Value = $"{item.TaxRate:F0}%"; // الضريبة %
                row.Cells[5].Value = item.DiscountAmount; // الخصم
                row.Cells[6].Value = item.Total; // المجموع
                row.Tag = item;

                // تلوين الصف حسب حالة الضريبة
                if (item.TaxRate > 0)
                {
                    if (item.IsTaxInclusive)
                    {
                        row.DefaultCellStyle.BackColor = Color.FromArgb(250, 255, 250); // أخضر فاتح للضريبة الشاملة
                    }
                    else
                    {
                        row.DefaultCellStyle.BackColor = Color.FromArgb(255, 250, 250); // أحمر فاتح للضريبة غير الشاملة
                    }
                }

                _salesGrid.Rows.Add(row);
            }
        }

        private void UpdateSummary()
        {
            _currentInvoice.CalculateTotal();

            // تحديث التسمية الرئيسية للإجمالي
            if (_totalLabel != null)
            {
                _totalLabel.Text = $"الإجمالي: {_currentInvoice.Total:F2} ر.س";
            }
        }

        // Helper Methods
        private async Task AddProductByBarcode(string barcode)
        {
            try
            {
                Product? product = null;

                // البحث بالباركود أولاً
                if (barcode.Length > 5)
                {
                    product = await _productService.GetProductByBarcodeAsync(barcode);
                }

                // إذا لم يوجد، البحث بالكود
                if (product == null)
                {
                    product = await _productService.GetProductByCodeAsync(barcode);
                }

                if (product != null)
                {
                    AddProductToInvoice(product, 1);
                }
                else
                {
                    MessageBox.Show("لم يتم العثور على المنتج", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث عن المنتج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void AddProductToInvoice(Product product, decimal quantity)
        {
            try
            {
                // التحقق من الصلاحيات إذا كان السعر مختلف
                var currentUser = _userService.GetCurrentUser();

                _currentInvoice.AddItem(product, quantity);
                RefreshSalesGrid();
                UpdateSummary();

                // تركيز على حقل الباركود
                _barcodeTextBox.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة المنتج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateInvoiceItemFromGrid(int rowIndex)
        {
            try
            {
                if (rowIndex >= _currentInvoice.Items.Count) return;

                var item = _currentInvoice.Items[rowIndex];
                var row = _salesGrid.Rows[rowIndex];

                // تحديث الكمية (العمود 2)
                if (decimal.TryParse(row.Cells[2].Value?.ToString(), out decimal quantity))
                {
                    item.Quantity = quantity;
                }

                // تحديث السعر (العمود 3) - مع التحقق من الصلاحيات
                if (decimal.TryParse(row.Cells[3].Value?.ToString(), out decimal unitPrice))
                {
                    if (unitPrice != item.UnitPrice)
                    {
                        if (!_userService.HasPermission("editprices"))
                        {
                            MessageBox.Show("ليس لديك صلاحية لتعديل الأسعار", "تنبيه",
                                MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            row.Cells[3].Value = item.UnitPrice;
                            return;
                        }
                    }
                    item.UnitPrice = unitPrice;
                }

                // تحديث الخصم (العمود 5) - مع التحقق من الصلاحيات
                if (decimal.TryParse(row.Cells[5].Value?.ToString(), out decimal discount))
                {
                    if (discount > 0 && !_userService.HasPermission("applydiscounts"))
                    {
                        MessageBox.Show("ليس لديك صلاحية لتطبيق الخصومات", "تنبيه",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        row.Cells[5].Value = 0;
                        return;
                    }
                    item.DiscountAmount = discount;
                }

                item.CalculateTotal();
                row.Cells[6].Value = item.Total; // العمود 6 للمجموع

                UpdateSummary();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث العنصر: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void DeleteSelectedItem()
        {
            try
            {
                if (_salesGrid.SelectedRows.Count == 0)
                {
                    MessageBox.Show("يرجى اختيار عنصر للحذف", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (!_userService.HasPermission("deleteitems"))
                {
                    MessageBox.Show("ليس لديك صلاحية لحذف العناصر", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var result = MessageBox.Show("هل أنت متأكد من حذف هذا العنصر؟", "تأكيد الحذف",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    var rowIndex = _salesGrid.SelectedRows[0].Index;
                    if (rowIndex < _currentInvoice.Items.Count)
                    {
                        _currentInvoice.Items.RemoveAt(rowIndex);
                        RefreshSalesGrid();
                        UpdateSummary();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف العنصر: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task SaveCurrentInvoice()
        {
            try
            {
                if (_currentInvoice.Items.Count == 0)
                {
                    MessageBox.Show("لا يمكن حفظ فاتورة فارغة", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // طلب المبلغ المدفوع
                var paymentForm = new PaymentForm(_currentInvoice.Total);
                if (paymentForm.ShowDialog() == DialogResult.OK)
                {
                    _currentInvoice.PaidAmount = paymentForm.PaidAmount;
                    _currentInvoice.PaymentMethod = paymentForm.PaymentMethod;
                    _currentInvoice.Status = InvoiceStatus.Completed;

                    var success = await _invoiceService.SaveInvoiceAsync(_currentInvoice);

                    if (success)
                    {
                        MessageBox.Show("تم حفظ الفاتورة بنجاح", "نجح",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);

                        UpdateSummary();

                        // سؤال عن الطباعة
                        var printResult = MessageBox.Show("هل تريد طباعة الفاتورة؟", "طباعة",
                            MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                        if (printResult == DialogResult.Yes)
                        {
                            await PrintCurrentInvoice();
                        }
                    }
                    else
                    {
                        MessageBox.Show("فشل في حفظ الفاتورة", "خطأ",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الفاتورة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task PrintCurrentInvoice()
        {
            try
            {
                if (_currentInvoice.Items.Count == 0)
                {
                    MessageBox.Show("لا يمكن طباعة فاتورة فارغة", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var success = await _invoiceService.PrintInvoiceAsync(_currentInvoice);

                if (success)
                {
                    MessageBox.Show("تم طباعة الفاتورة بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("فشل في طباعة الفاتورة", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة الفاتورة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CreateNewInvoice()
        {
            try
            {
                if (_currentInvoice.Items.Count > 0)
                {
                    var result = MessageBox.Show("هناك فاتورة حالية غير محفوظة. هل تريد المتابعة؟",
                        "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                    if (result != DialogResult.Yes)
                        return;
                }

                InitializeNewInvoice();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء فاتورة جديدة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task LogoutUser()
        {
            try
            {
                var result = MessageBox.Show("هل أنت متأكد من تسجيل الخروج؟", "تأكيد",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    await _userService.LogoutAsync();
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تسجيل الخروج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task UpdateSyncStatus()
        {
            try
            {
                var isOnline = await _offlineDataService.IsOnlineAsync();
                var pendingCount = await _offlineDataService.GetPendingInvoicesCountAsync();

                if (isOnline)
                {
                    if (pendingCount > 0)
                    {
                        _syncStatusLabel.Text = $"🔄 مزامنة ({pendingCount} معلق)";
                        _syncStatusLabel.ForeColor = Color.Orange;

                        // محاولة مزامنة تلقائية
                        _ = Task.Run(async () => await _offlineDataService.SyncOfflineDataAsync());
                    }
                    else
                    {
                        _syncStatusLabel.Text = "✅ متصل";
                        _syncStatusLabel.ForeColor = Color.LightGreen;
                    }
                }
                else
                {
                    if (pendingCount > 0)
                    {
                        _syncStatusLabel.Text = $"❌ غير متصل ({pendingCount} معلق)";
                        _syncStatusLabel.ForeColor = Color.Red;
                    }
                    else
                    {
                        _syncStatusLabel.Text = "❌ غير متصل";
                        _syncStatusLabel.ForeColor = Color.Red;
                    }
                }
            }
            catch (Exception)
            {
                _syncStatusLabel.Text = "⚠️ خطأ في الحالة";
                _syncStatusLabel.ForeColor = Color.Yellow;
            }
        }

        private async Task ProcessPayment()
        {
            try
            {
                if (_currentInvoice.Items.Count == 0)
                {
                    MessageBox.Show("لا يمكن إتمام الدفع لفاتورة فارغة", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // طلب المبلغ المدفوع
                var paymentForm = new PaymentForm(_currentInvoice.Total);
                if (paymentForm.ShowDialog() == DialogResult.OK)
                {
                    _currentInvoice.PaidAmount = paymentForm.PaidAmount;
                    _currentInvoice.PaymentMethod = paymentForm.PaymentMethod;
                    _currentInvoice.Status = InvoiceStatus.Completed;

                    var success = await _invoiceService.SaveInvoiceAsync(_currentInvoice);

                    if (success)
                    {
                        MessageBox.Show("تم إتمام الدفع وحفظ الفاتورة بنجاح", "نجح",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);

                        UpdateSummary();

                        // سؤال عن الطباعة
                        var printResult = MessageBox.Show("هل تريد طباعة الفاتورة؟", "طباعة",
                            MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                        if (printResult == DialogResult.Yes)
                        {
                            await PrintCurrentInvoice();
                        }

                        // إنشاء فاتورة جديدة
                        var newInvoiceResult = MessageBox.Show("هل تريد إنشاء فاتورة جديدة؟", "فاتورة جديدة",
                            MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                        if (newInvoiceResult == DialogResult.Yes)
                        {
                            CreateNewInvoice();
                        }
                    }
                    else
                    {
                        MessageBox.Show("فشل في حفظ الفاتورة", "خطأ",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إتمام الدفع: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task SyncOfflineData()
        {
            try
            {
                var pendingCount = await _offlineDataService.GetPendingInvoicesCountAsync();

                if (pendingCount == 0)
                {
                    MessageBox.Show("لا توجد بيانات معلقة للمزامنة", "معلومات",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                var result = MessageBox.Show($"سيتم مزامنة {pendingCount} فاتورة معلقة. هل تريد المتابعة؟",
                    "تأكيد المزامنة", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result != DialogResult.Yes) return;

                // تعطيل الواجهة أثناء المزامنة
                this.Enabled = false;
                _syncStatusLabel.Text = "🔄 جاري المزامنة...";
                _syncStatusLabel.ForeColor = Color.Orange;

                var success = await _offlineDataService.SyncOfflineDataAsync();

                if (success)
                {
                    MessageBox.Show("تم إرسال جميع البيانات بنجاح", "نجحت المزامنة",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    var remainingCount = await _offlineDataService.GetPendingInvoicesCountAsync();
                    MessageBox.Show($"تم إرسال بعض البيانات. يتبقى {remainingCount} فاتورة",
                        "مزامنة جزئية", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في المزامنة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                this.Enabled = true;
                await UpdateSyncStatus();
            }
        }
    }
}
