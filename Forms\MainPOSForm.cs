using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using POSSystem.Models;
using POSSystem.Services;

namespace POSSystem.Forms
{
    public partial class MainPOSForm : Form
    {
        private readonly IUserService _userService;
        private readonly IProductService _productService;
        private readonly IInvoiceService _invoiceService;
        private readonly IPrintService _printService;
        private readonly IOfflineDataService _offlineDataService;

        private Invoice _currentInvoice;
        private List<Product> _products = new();
        private List<Category> _categories = new();

        public MainPOSForm(IUserService userService, IProductService productService,
            IInvoiceService invoiceService, IPrintService printService, IOfflineDataService offlineDataService)
        {
            _userService = userService;
            _productService = productService;
            _invoiceService = invoiceService;
            _printService = printService;
            _offlineDataService = offlineDataService;
            
            InitializeComponent();
            SetupForm();
            InitializeNewInvoice();
            LoadData();
        }

        private void SetupForm()
        {
            // ربط العناصر من Designer
            SetupSalesGrid();
            SetupEventHandlers();

            // إعداد المستخدم الحالي
            var currentUser = _userService.GetCurrentUser();
            if (currentUser != null)
            {
                userLabel.Text = $"المستخدم: {currentUser.Username}";
            }

            // تحديث الوقت
            dateTimeLabel.Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm");
        }

        private void SetupSalesGrid()
        {
            salesGrid.Columns.Clear();

            // إعداد الأعمدة
            salesGrid.Columns.Add("ProductCode", "الكود");
            salesGrid.Columns.Add("ProductName", "اسم المنتج");
            salesGrid.Columns.Add("Quantity", "الكمية");
            salesGrid.Columns.Add("UnitPrice", "السعر الوحدة");
            salesGrid.Columns.Add("TaxRate", "الضريبة %");
            salesGrid.Columns.Add("Discount", "الخصم");
            salesGrid.Columns.Add("Total", "المجموع");

            // تخصيص الأعمدة
            salesGrid.Columns["ProductCode"].Width = 120;
            salesGrid.Columns["ProductName"].Width = 250;
            salesGrid.Columns["Quantity"].Width = 90;
            salesGrid.Columns["UnitPrice"].Width = 110;
            salesGrid.Columns["TaxRate"].Width = 100;
            salesGrid.Columns["Discount"].Width = 90;
            salesGrid.Columns["Total"].Width = 130;

            // جعل أعمدة معينة قابلة للتحرير
            salesGrid.Columns["ProductCode"].ReadOnly = false;
            salesGrid.Columns["ProductName"].ReadOnly = true;
            salesGrid.Columns["UnitPrice"].ReadOnly = false;
            salesGrid.Columns["TaxRate"].ReadOnly = true;
            salesGrid.Columns["Total"].ReadOnly = true;

            // تنسيق الأعمدة
            salesGrid.Columns["Quantity"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            salesGrid.Columns["UnitPrice"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            salesGrid.Columns["TaxRate"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            salesGrid.Columns["Discount"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            salesGrid.Columns["Total"].DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;

            // تنسيق الأرقام
            salesGrid.Columns["UnitPrice"].DefaultCellStyle.Format = "F2";
            salesGrid.Columns["Discount"].DefaultCellStyle.Format = "F2";
            salesGrid.Columns["Total"].DefaultCellStyle.Format = "F2";

            // ألوان الصفوف المتناوبة
            salesGrid.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 249, 250);
            salesGrid.DefaultCellStyle.BackColor = Color.White;
            salesGrid.DefaultCellStyle.SelectionBackColor = Color.FromArgb(52, 152, 219);
            salesGrid.DefaultCellStyle.SelectionForeColor = Color.White;

            // تنسيق رأس الأعمدة المحسن
            salesGrid.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(41, 128, 185);
            salesGrid.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            salesGrid.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 12, FontStyle.Bold);
            salesGrid.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            salesGrid.ColumnHeadersDefaultCellStyle.Padding = new Padding(5);
            salesGrid.ColumnHeadersHeight = 45;

            // إعدادات عامة محسنة
            salesGrid.EnableHeadersVisualStyles = false;
            salesGrid.GridColor = Color.FromArgb(149, 165, 166);
            salesGrid.CellBorderStyle = DataGridViewCellBorderStyle.Single;
            salesGrid.ColumnHeadersBorderStyle = DataGridViewHeaderBorderStyle.Single;
            salesGrid.RowHeadersVisible = false;
            salesGrid.DefaultCellStyle.Font = new Font("Segoe UI", 11);
            salesGrid.DefaultCellStyle.Padding = new Padding(8, 5, 8, 5);
            salesGrid.RowTemplate.Height = 40;
        }

        private void SetupEventHandlers()
        {
            // معالج الباركود
            barcodeTextBox.KeyPress += BarcodeTextBox_KeyPress;
            
            // معالج تحرير الجدول
            salesGrid.CellValueChanged += SalesGrid_CellValueChanged;
            salesGrid.KeyDown += SalesGrid_KeyDown;

            // معالجات الأزرار
            saveButton.Click += SaveInvoice_Click;
            printButton.Click += PrintInvoice_Click;
            payButton.Click += async (s, e) => await ProcessPayment();
            deleteButton.Click += DeleteItem_Click;
            newButton.Click += NewInvoice_Click;
            syncButton.Click += SyncData_Click;
            logoutButton.Click += Logout_Click;
            
            // تحديث الوقت وحالة المزامنة
            var timer = new System.Windows.Forms.Timer { Interval = 1000 };
            timer.Tick += async (s, e) =>
            {
                dateTimeLabel.Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm");
                await UpdateSyncStatus();
            };
            timer.Start();
        }

        private async void InitializeNewInvoice()
        {
            _currentInvoice = new Invoice
            {
                InvoiceDate = DateTime.Now,
                UserId = _userService.GetCurrentUser()?.Id ?? 0,
                User = _userService.GetCurrentUser(),
                Status = InvoiceStatus.Pending
            };

            // الحصول على رقم فاتورة جديد
            _currentInvoice.InvoiceNumber = await _invoiceService.GetNewInvoiceNumberAsync();
            invoiceNumberLabel.Text = $"رقم الفاتورة: {_currentInvoice.InvoiceNumber}";

            RefreshSalesGrid();
            UpdateSummary();
        }

        private async void LoadData()
        {
            try
            {
                // تحميل الفئات والمنتجات
                _categories = await _productService.GetCategoriesAsync();
                _products = await _productService.SearchProductsAsync(new ProductSearchRequest { PageSize = 100 });
                
                LoadProductButtons();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadProductButtons()
        {
            productsPanel.Controls.Clear();

            // إضافة جميع المنتجات على شكل مربعات صغيرة
            var allProducts = _products.Where(p => p.IsActive).ToList();
            
            foreach (var product in allProducts)
            {
                // مربع المنتج
                var productButton = new Button
                {
                    Size = new Size(85, 85),
                    BackColor = Color.White,
                    FlatStyle = FlatStyle.Flat,
                    Margin = new Padding(3),
                    Tag = product,
                    Cursor = Cursors.Hand,
                    TextAlign = ContentAlignment.BottomCenter,
                    Font = new Font("Segoe UI", 8, FontStyle.Bold),
                    ForeColor = Color.FromArgb(44, 62, 80)
                };

                // تحديد لون الحدود حسب الفئة
                var category = _categories.FirstOrDefault(c => c.Id == product.CategoryId);
                var borderColor = category != null ? ColorTranslator.FromHtml(category.Color ?? "#3498db") : Color.FromArgb(52, 152, 219);
                
                productButton.FlatAppearance.BorderColor = borderColor;
                productButton.FlatAppearance.BorderSize = 2;

                // النص: اسم المنتج والسعر
                productButton.Text = $"{product.Name}\n{product.Price:F2} ر.س";

                // تأثيرات الهوفر
                productButton.MouseEnter += (s, e) =>
                {
                    productButton.BackColor = Color.FromArgb(240, 248, 255);
                    productButton.FlatAppearance.BorderSize = 3;
                };

                productButton.MouseLeave += (s, e) =>
                {
                    productButton.BackColor = Color.White;
                    productButton.FlatAppearance.BorderSize = 2;
                };

                // معالج النقر
                productButton.Click += ProductButton_Click;

                productsPanel.Controls.Add(productButton);
            }
        }

        private void RefreshSalesGrid()
        {
            salesGrid.Rows.Clear();

            foreach (var item in _currentInvoice.Items)
            {
                var row = new DataGridViewRow();
                row.CreateCells(salesGrid);

                row.Cells[0].Value = item.ProductCode ?? "";
                row.Cells[1].Value = item.ProductName;
                row.Cells[2].Value = item.Quantity;
                row.Cells[3].Value = item.UnitPrice;
                row.Cells[4].Value = $"{item.TaxRate:F0}%";
                row.Cells[5].Value = item.DiscountAmount;
                row.Cells[6].Value = item.Total;
                row.Tag = item;

                // تلوين الصف حسب حالة الضريبة
                if (item.TaxRate > 0)
                {
                    if (item.IsTaxInclusive)
                    {
                        row.DefaultCellStyle.BackColor = Color.FromArgb(250, 255, 250);
                    }
                    else
                    {
                        row.DefaultCellStyle.BackColor = Color.FromArgb(255, 250, 250);
                    }
                }

                salesGrid.Rows.Add(row);
            }

            // إضافة صف فارغ للكتابة
            AddEmptyRow();
        }

        private void AddEmptyRow()
        {
            var emptyRow = new DataGridViewRow();
            emptyRow.CreateCells(salesGrid);
            
            for (int i = 0; i < 7; i++)
            {
                emptyRow.Cells[i].Value = "";
            }
            
            emptyRow.DefaultCellStyle.BackColor = Color.FromArgb(252, 252, 252);
            salesGrid.Rows.Add(emptyRow);
        }

        private void UpdateSummary()
        {
            _currentInvoice.CalculateTotal();
            totalLabel.Text = $"الإجمالي: {_currentInvoice.Total:F2} ر.س";
        }

        // Event Handlers
        private async void BarcodeTextBox_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                var barcode = barcodeTextBox.Text.Trim();
                if (!string.IsNullOrEmpty(barcode))
                {
                    await AddProductByBarcode(barcode);
                    barcodeTextBox.Clear();
                }
                e.Handled = true;
            }
        }

        private void ProductButton_Click(object sender, EventArgs e)
        {
            if (sender is Button button && button.Tag is Product product)
            {
                AddProductToInvoice(product, 1);
            }
        }

        private async void SalesGrid_CellValueChanged(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0 && e.ColumnIndex >= 0)
            {
                var columnName = salesGrid.Columns[e.ColumnIndex].Name;

                if (columnName == "ProductCode")
                {
                    var code = salesGrid.Rows[e.RowIndex].Cells[e.ColumnIndex].Value?.ToString();
                    if (!string.IsNullOrEmpty(code))
                    {
                        await AddProductByCodeToRow(code, e.RowIndex);
                    }
                }
                else if (columnName == "Quantity" || columnName == "UnitPrice" || columnName == "Discount")
                {
                    UpdateInvoiceItemFromGrid(e.RowIndex);
                }
            }
        }

        private void SalesGrid_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Delete && salesGrid.SelectedRows.Count > 0)
            {
                DeleteSelectedItem();
            }
        }

        private async void SaveInvoice_Click(object sender, EventArgs e)
        {
            await SaveCurrentInvoice();
        }

        private async void PrintInvoice_Click(object sender, EventArgs e)
        {
            await PrintCurrentInvoice();
        }

        private void DeleteItem_Click(object sender, EventArgs e)
        {
            DeleteSelectedItem();
        }

        private void NewInvoice_Click(object sender, EventArgs e)
        {
            CreateNewInvoice();
        }

        private async void Logout_Click(object sender, EventArgs e)
        {
            await LogoutUser();
        }

        private async void SyncData_Click(object sender, EventArgs e)
        {
            await SyncOfflineData();
        }

        // Helper Methods
        private async Task AddProductByBarcode(string barcode)
        {
            try
            {
                Product? product = null;

                if (barcode.Length > 5)
                {
                    product = await _productService.GetProductByBarcodeAsync(barcode);
                }

                if (product == null)
                {
                    product = await _productService.GetProductByCodeAsync(barcode);
                }

                if (product != null)
                {
                    AddProductToInvoice(product, 1);
                }
                else
                {
                    MessageBox.Show("لم يتم العثور على المنتج", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث عن المنتج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void AddProductToInvoice(Product product, decimal quantity)
        {
            try
            {
                _currentInvoice.AddItem(product, quantity);
                RefreshSalesGrid();
                UpdateSummary();
                barcodeTextBox.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة المنتج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task AddProductByCodeToRow(string code, int rowIndex)
        {
            try
            {
                Product? product = null;

                if (code.Length > 5)
                {
                    product = await _productService.GetProductByBarcodeAsync(code);
                }

                if (product == null)
                {
                    product = await _productService.GetProductByCodeAsync(code);
                }

                if (product != null)
                {
                    if (rowIndex >= _currentInvoice.Items.Count)
                    {
                        AddProductToInvoice(product, 1);
                    }
                    else
                    {
                        var item = _currentInvoice.Items[rowIndex];
                        item.ProductId = product.Id;
                        item.ProductCode = product.Code;
                        item.ProductName = product.Name;
                        item.UnitPrice = product.Price;
                        item.TaxRate = product.TaxRate;
                        item.IsTaxInclusive = product.IsTaxInclusive;
                        item.CalculateTotal();

                        RefreshSalesGrid();
                        UpdateSummary();
                    }
                }
                else
                {
                    MessageBox.Show("لم يتم العثور على المنتج", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    salesGrid.Rows[rowIndex].Cells["ProductCode"].Value = "";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث عن المنتج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateInvoiceItemFromGrid(int rowIndex)
        {
            try
            {
                if (rowIndex >= _currentInvoice.Items.Count) return;

                var item = _currentInvoice.Items[rowIndex];
                var row = salesGrid.Rows[rowIndex];

                if (decimal.TryParse(row.Cells[2].Value?.ToString(), out decimal quantity))
                {
                    item.Quantity = quantity;
                }

                if (decimal.TryParse(row.Cells[3].Value?.ToString(), out decimal unitPrice))
                {
                    item.UnitPrice = unitPrice;
                }

                if (decimal.TryParse(row.Cells[5].Value?.ToString(), out decimal discount))
                {
                    item.DiscountAmount = discount;
                }

                item.CalculateTotal();
                row.Cells[6].Value = item.Total;
                UpdateSummary();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث العنصر: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void DeleteSelectedItem()
        {
            try
            {
                if (salesGrid.SelectedRows.Count == 0)
                {
                    MessageBox.Show("يرجى اختيار عنصر للحذف", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var result = MessageBox.Show("هل أنت متأكد من حذف هذا العنصر؟", "تأكيد الحذف",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    var rowIndex = salesGrid.SelectedRows[0].Index;
                    if (rowIndex < _currentInvoice.Items.Count)
                    {
                        _currentInvoice.Items.RemoveAt(rowIndex);
                        RefreshSalesGrid();
                        UpdateSummary();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف العنصر: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task SaveCurrentInvoice()
        {
            try
            {
                if (_currentInvoice.Items.Count == 0)
                {
                    MessageBox.Show("لا يمكن حفظ فاتورة فارغة", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var paymentForm = new PaymentForm(_currentInvoice.Total);
                if (paymentForm.ShowDialog() == DialogResult.OK)
                {
                    _currentInvoice.PaidAmount = paymentForm.PaidAmount;
                    _currentInvoice.PaymentMethod = paymentForm.PaymentMethod;
                    _currentInvoice.Status = InvoiceStatus.Completed;

                    var success = await _invoiceService.SaveInvoiceAsync(_currentInvoice);

                    if (success)
                    {
                        MessageBox.Show("تم حفظ الفاتورة بنجاح", "نجح",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        UpdateSummary();
                    }
                    else
                    {
                        MessageBox.Show("فشل في حفظ الفاتورة", "خطأ",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الفاتورة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task PrintCurrentInvoice()
        {
            try
            {
                if (_currentInvoice.Items.Count == 0)
                {
                    MessageBox.Show("لا يمكن طباعة فاتورة فارغة", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var success = await _invoiceService.PrintInvoiceAsync(_currentInvoice);

                if (success)
                {
                    MessageBox.Show("تم طباعة الفاتورة بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("فشل في طباعة الفاتورة", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة الفاتورة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CreateNewInvoice()
        {
            try
            {
                if (_currentInvoice.Items.Count > 0)
                {
                    var result = MessageBox.Show("هناك فاتورة حالية غير محفوظة. هل تريد المتابعة؟",
                        "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                    if (result != DialogResult.Yes)
                        return;
                }

                InitializeNewInvoice();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء فاتورة جديدة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task LogoutUser()
        {
            try
            {
                var result = MessageBox.Show("هل أنت متأكد من تسجيل الخروج؟", "تأكيد",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    await _userService.LogoutAsync();
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تسجيل الخروج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task UpdateSyncStatus()
        {
            try
            {
                var isOnline = await _offlineDataService.IsOnlineAsync();
                var pendingCount = await _offlineDataService.GetPendingInvoicesCountAsync();

                if (isOnline)
                {
                    if (pendingCount > 0)
                    {
                        syncStatusLabel.Text = $"🔄 مزامنة ({pendingCount} معلق)";
                        syncStatusLabel.ForeColor = Color.Orange;
                    }
                    else
                    {
                        syncStatusLabel.Text = "✅ متصل";
                        syncStatusLabel.ForeColor = Color.LightGreen;
                    }
                }
                else
                {
                    syncStatusLabel.Text = "❌ غير متصل";
                    syncStatusLabel.ForeColor = Color.Red;
                }
            }
            catch (Exception)
            {
                syncStatusLabel.Text = "⚠️ خطأ في الحالة";
                syncStatusLabel.ForeColor = Color.Yellow;
            }
        }

        private async Task ProcessPayment()
        {
            try
            {
                if (_currentInvoice.Items.Count == 0)
                {
                    MessageBox.Show("لا يمكن إتمام الدفع لفاتورة فارغة", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var paymentForm = new PaymentForm(_currentInvoice.Total);
                if (paymentForm.ShowDialog() == DialogResult.OK)
                {
                    _currentInvoice.PaidAmount = paymentForm.PaidAmount;
                    _currentInvoice.PaymentMethod = paymentForm.PaymentMethod;
                    _currentInvoice.Status = InvoiceStatus.Completed;

                    var success = await _invoiceService.SaveInvoiceAsync(_currentInvoice);

                    if (success)
                    {
                        MessageBox.Show("تم إتمام الدفع وحفظ الفاتورة بنجاح", "نجح",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        UpdateSummary();
                        CreateNewInvoice();
                    }
                    else
                    {
                        MessageBox.Show("فشل في حفظ الفاتورة", "خطأ",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إتمام الدفع: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task SyncOfflineData()
        {
            try
            {
                var pendingCount = await _offlineDataService.GetPendingInvoicesCountAsync();

                if (pendingCount == 0)
                {
                    MessageBox.Show("لا توجد بيانات معلقة للمزامنة", "معلومات",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                var result = MessageBox.Show($"سيتم مزامنة {pendingCount} فاتورة معلقة. هل تريد المتابعة؟",
                    "تأكيد المزامنة", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result != DialogResult.Yes) return;

                this.Enabled = false;
                syncStatusLabel.Text = "🔄 جاري المزامنة...";
                syncStatusLabel.ForeColor = Color.Orange;

                var success = await _offlineDataService.SyncOfflineDataAsync();

                if (success)
                {
                    MessageBox.Show("تم إرسال جميع البيانات بنجاح", "نجحت المزامنة",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    var remainingCount = await _offlineDataService.GetPendingInvoicesCountAsync();
                    MessageBox.Show($"تم إرسال بعض البيانات. يتبقى {remainingCount} فاتورة",
                        "مزامنة جزئية", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في المزامنة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                this.Enabled = true;
                await UpdateSyncStatus();
            }
        }
    }
}
