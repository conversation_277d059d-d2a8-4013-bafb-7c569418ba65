using POSSystem.Models;
using POSSystem.Services;
using System.Data;

namespace POSSystem.Forms
{
    public partial class MainPOSForm : Form
    {
        private readonly IUserService _userService;
        private readonly IProductService _productService;
        private readonly IInvoiceService _invoiceService;
        private readonly IPrintService _printService;
        private readonly IOfflineDataService _offlineDataService;

        private Invoice _currentInvoice;
        private List<Product> _products = new();
        private List<Category> _categories = new();
        
        // Controls
        private DataGridView _salesGrid;
        private FlowLayoutPanel _productsPanel;
        private Panel _summaryPanel;
        private TextBox _barcodeTextBox;
        private Label _totalLabel;
        private Label _invoiceNumberLabel;
        private Label _userLabel;
        private Label _dateTimeLabel;
        private Label _syncStatusLabel;

        public MainPOSForm(IUserService userService, IProductService productService,
            IInvoiceService invoiceService, IPrintService printService, IOfflineDataService offlineDataService)
        {
            _userService = userService;
            _productService = productService;
            _invoiceService = invoiceService;
            _printService = printService;
            _offlineDataService = offlineDataService;
            
            InitializeComponent();
            SetupForm();
            InitializeNewInvoice();
            LoadData();
        }

        private void SetupForm()
        {
            // إعداد النموذج الرئيسي
            this.Text = "نظام نقطة البيع - الشاشة الرئيسية";
            this.WindowState = FormWindowState.Maximized;
            this.BackColor = Color.FromArgb(245, 245, 245);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.KeyPreview = true;

            CreateLayout();
            SetupEventHandlers();
        }

        private void CreateLayout()
        {
            // لوحة علوية - معلومات الفاتورة
            var topPanel = CreateTopPanel();
            
            // لوحة وسطى - المحتوى الرئيسي
            var mainPanel = CreateMainPanel();
            
            // لوحة سفلية - أزرار العمليات
            var bottomPanel = CreateBottomPanel();

            // إضافة اللوحات للنموذج
            this.Controls.Add(topPanel);
            this.Controls.Add(mainPanel);
            this.Controls.Add(bottomPanel);

            // ترتيب اللوحات
            topPanel.Dock = DockStyle.Top;
            bottomPanel.Dock = DockStyle.Bottom;
            mainPanel.Dock = DockStyle.Fill;
        }

        private Panel CreateTopPanel()
        {
            var panel = new Panel
            {
                Height = 80,
                BackColor = Color.FromArgb(52, 152, 219),
                Padding = new Padding(10)
            };

            // معلومات الفاتورة
            _invoiceNumberLabel = new Label
            {
                Text = "رقم الفاتورة: جاري التحميل...",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(20, 15),
                AutoSize = true
            };

            _dateTimeLabel = new Label
            {
                Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm"),
                Font = new Font("Segoe UI", 11),
                ForeColor = Color.White,
                Location = new Point(20, 40),
                AutoSize = true
            };

            _userLabel = new Label
            {
                Text = $"المستخدم: {_userService.GetCurrentUser()?.FullName ?? "غير محدد"}",
                Font = new Font("Segoe UI", 11),
                ForeColor = Color.White,
                Anchor = AnchorStyles.Top | AnchorStyles.Right,
                AutoSize = true
            };

            // حقل الباركود
            var barcodeLabel = new Label
            {
                Text = "الباركود/الكود:",
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.White,
                Location = new Point(400, 15),
                AutoSize = true
            };

            _barcodeTextBox = new TextBox
            {
                Font = new Font("Segoe UI", 11),
                Size = new Size(200, 30),
                Location = new Point(400, 40)
            };

            // مؤشر حالة المزامنة
            _syncStatusLabel = new Label
            {
                Text = "🔄 جاري التحقق...",
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.White,
                Location = new Point(650, 45),
                AutoSize = true
            };

            panel.Controls.AddRange(new Control[]
            {
                _invoiceNumberLabel, _dateTimeLabel, _userLabel, barcodeLabel, _barcodeTextBox, _syncStatusLabel
            });

            // تحديث موقع تسمية المستخدم
            panel.Resize += (s, e) =>
            {
                _userLabel.Location = new Point(panel.Width - _userLabel.Width - 20, 15);
            };

            return panel;
        }

        private Panel CreateMainPanel()
        {
            var panel = new Panel
            {
                Padding = new Padding(10)
            };

            // تقسيم اللوحة إلى ثلاثة أجزاء
            var leftPanel = CreateLeftPanel();    // قائمة المنتجات
            var centerPanel = CreateCenterPanel(); // جدول المبيعات
            var rightPanel = CreateRightPanel();   // ملخص الفاتورة

            panel.Controls.AddRange(new Control[] { leftPanel, centerPanel, rightPanel });

            // ترتيب اللوحات
            leftPanel.Dock = DockStyle.Right;
            rightPanel.Dock = DockStyle.Left;
            centerPanel.Dock = DockStyle.Fill;

            return panel;
        }

        private Panel CreateLeftPanel()
        {
            var panel = new Panel
            {
                Width = 300,
                BackColor = Color.White,
                Padding = new Padding(5)
            };

            // عنوان قائمة المنتجات
            var titleLabel = new Label
            {
                Text = "قائمة المنتجات",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                Dock = DockStyle.Top,
                Height = 30,
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White
            };

            // لوحة المنتجات
            _productsPanel = new FlowLayoutPanel
            {
                Dock = DockStyle.Fill,
                AutoScroll = true,
                FlowDirection = FlowDirection.TopDown,
                WrapContents = false,
                Padding = new Padding(5)
            };

            panel.Controls.Add(titleLabel);
            panel.Controls.Add(_productsPanel);

            return panel;
        }

        private Panel CreateCenterPanel()
        {
            var panel = new Panel
            {
                BackColor = Color.White,
                Padding = new Padding(5, 5, 5, 5),
                Margin = new Padding(5)
            };

            // عنوان جدول المبيعات
            var titleLabel = new Label
            {
                Text = "عناصر الفاتورة",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                Dock = DockStyle.Top,
                Height = 30,
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.FromArgb(155, 89, 182),
                ForeColor = Color.White
            };

            // جدول المبيعات
            _salesGrid = new DataGridView
            {
                Dock = DockStyle.Fill,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                MultiSelect = false,
                ReadOnly = false,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.None,
                ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize,
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill
            };

            SetupSalesGrid();

            panel.Controls.Add(titleLabel);
            panel.Controls.Add(_salesGrid);

            return panel;
        }

        private Panel CreateRightPanel()
        {
            var panel = new Panel
            {
                Width = 250,
                BackColor = Color.White,
                Padding = new Padding(5)
            };

            // عنوان ملخص الفاتورة
            var titleLabel = new Label
            {
                Text = "ملخص الفاتورة",
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                Dock = DockStyle.Top,
                Height = 30,
                TextAlign = ContentAlignment.MiddleCenter,
                BackColor = Color.FromArgb(230, 126, 34),
                ForeColor = Color.White
            };

            _summaryPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(10)
            };

            CreateSummaryControls();

            panel.Controls.Add(titleLabel);
            panel.Controls.Add(_summaryPanel);

            return panel;
        }

        private Panel CreateBottomPanel()
        {
            var panel = new Panel
            {
                Height = 80,
                BackColor = Color.FromArgb(44, 62, 80),
                Padding = new Padding(10)
            };

            // أزرار العمليات
            var saveButton = CreateActionButton("حفظ الفاتورة", Color.FromArgb(46, 204, 113), SaveInvoice_Click);
            var printButton = CreateActionButton("طباعة", Color.FromArgb(52, 152, 219), PrintInvoice_Click);
            var deleteButton = CreateActionButton("حذف صنف", Color.FromArgb(231, 76, 60), DeleteItem_Click);
            var newButton = CreateActionButton("فاتورة جديدة", Color.FromArgb(155, 89, 182), NewInvoice_Click);
            var syncButton = CreateActionButton("مزامنة", Color.FromArgb(243, 156, 18), SyncData_Click);
            var logoutButton = CreateActionButton("تسجيل الخروج", Color.FromArgb(149, 165, 166), Logout_Click);

            // ترتيب الأزرار
            saveButton.Location = new Point(20, 20);
            printButton.Location = new Point(140, 20);
            deleteButton.Location = new Point(260, 20);
            newButton.Location = new Point(380, 20);
            syncButton.Location = new Point(500, 20);
            logoutButton.Location = new Point(panel.Width - 140, 20);
            logoutButton.Anchor = AnchorStyles.Top | AnchorStyles.Right;

            panel.Controls.AddRange(new Control[]
            {
                saveButton, printButton, deleteButton, newButton, syncButton, logoutButton
            });

            return panel;
        }

        private Button CreateActionButton(string text, Color backColor, EventHandler clickHandler)
        {
            var button = new Button
            {
                Text = text,
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                Size = new Size(110, 40),
                BackColor = backColor,
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            
            button.FlatAppearance.BorderSize = 0;
            button.Click += clickHandler;
            
            return button;
        }

        private void SetupSalesGrid()
        {
            _salesGrid.Columns.Clear();
            
            _salesGrid.Columns.Add("ProductName", "اسم المنتج");
            _salesGrid.Columns.Add("Quantity", "الكمية");
            _salesGrid.Columns.Add("UnitPrice", "السعر");
            _salesGrid.Columns.Add("Discount", "الخصم");
            _salesGrid.Columns.Add("Total", "المجموع");

            // تخصيص الأعمدة
            _salesGrid.Columns["ProductName"].Width = 200;
            _salesGrid.Columns["Quantity"].Width = 80;
            _salesGrid.Columns["UnitPrice"].Width = 80;
            _salesGrid.Columns["Discount"].Width = 80;
            _salesGrid.Columns["Total"].Width = 100;

            // جعل أعمدة معينة قابلة للتحرير
            _salesGrid.Columns["ProductName"].ReadOnly = true;
            _salesGrid.Columns["Total"].ReadOnly = true;
        }

        private void CreateSummaryControls()
        {
            var y = 20;
            var lineHeight = 35;

            // المجموع الفرعي
            CreateSummaryLabel("المجموع الفرعي:", "0.00 ر.س", ref y, lineHeight);
            
            // الخصم
            CreateSummaryLabel("الخصم:", "0.00 ر.س", ref y, lineHeight);
            
            // الضريبة
            CreateSummaryLabel("الضريبة:", "0.00 ر.س", ref y, lineHeight);
            
            // الإجمالي
            _totalLabel = CreateSummaryLabel("الإجمالي:", "0.00 ر.س", ref y, lineHeight, true);
            
            // المدفوع
            CreateSummaryLabel("المدفوع:", "0.00 ر.س", ref y, lineHeight);
            
            // الباقي
            CreateSummaryLabel("الباقي:", "0.00 ر.س", ref y, lineHeight);
        }

        private Label CreateSummaryLabel(string labelText, string valueText, ref int y, int lineHeight, bool isTotal = false)
        {
            var label = new Label
            {
                Text = labelText,
                Font = new Font("Segoe UI", isTotal ? 12 : 10, isTotal ? FontStyle.Bold : FontStyle.Regular),
                Location = new Point(10, y),
                Size = new Size(100, 25),
                TextAlign = ContentAlignment.MiddleRight
            };

            var valueLabel = new Label
            {
                Name = labelText.Replace(":", "").Replace(" ", "") + "Value",
                Text = valueText,
                Font = new Font("Segoe UI", isTotal ? 12 : 10, isTotal ? FontStyle.Bold : FontStyle.Regular),
                Location = new Point(120, y),
                Size = new Size(100, 25),
                TextAlign = ContentAlignment.MiddleLeft,
                ForeColor = isTotal ? Color.FromArgb(46, 204, 113) : Color.Black
            };

            _summaryPanel.Controls.Add(label);
            _summaryPanel.Controls.Add(valueLabel);

            y += lineHeight;
            
            return isTotal ? valueLabel : label;
        }

        private async void InitializeNewInvoice()
        {
            _currentInvoice = new Invoice
            {
                InvoiceDate = DateTime.Now,
                UserId = _userService.GetCurrentUser()?.Id ?? 0,
                User = _userService.GetCurrentUser(),
                Status = InvoiceStatus.Pending
            };

            // الحصول على رقم فاتورة جديد
            _currentInvoice.InvoiceNumber = await _invoiceService.GetNewInvoiceNumberAsync();
            _invoiceNumberLabel.Text = $"رقم الفاتورة: {_currentInvoice.InvoiceNumber}";

            RefreshSalesGrid();
            UpdateSummary();
        }

        private async void LoadData()
        {
            try
            {
                // تحميل الفئات والمنتجات
                _categories = await _productService.GetCategoriesAsync();
                _products = await _productService.SearchProductsAsync(new ProductSearchRequest { PageSize = 100 });
                
                LoadProductButtons();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadProductButtons()
        {
            _productsPanel.Controls.Clear();

            foreach (var category in _categories)
            {
                // عنوان الفئة
                var categoryLabel = new Label
                {
                    Text = category.Name,
                    Font = new Font("Segoe UI", 10, FontStyle.Bold),
                    BackColor = ColorTranslator.FromHtml(category.Color ?? "#3498db"),
                    ForeColor = Color.White,
                    Size = new Size(280, 30),
                    TextAlign = ContentAlignment.MiddleCenter,
                    Margin = new Padding(5, 10, 5, 5)
                };

                _productsPanel.Controls.Add(categoryLabel);

                // منتجات الفئة
                var categoryProducts = _products.Where(p => p.CategoryId == category.Id && p.IsActive).ToList();
                
                foreach (var product in categoryProducts)
                {
                    var productButton = new Button
                    {
                        Text = $"{product.Name}\n{product.Price:F2} ر.س",
                        Font = new Font("Segoe UI", 9),
                        Size = new Size(280, 50),
                        BackColor = Color.White,
                        ForeColor = Color.Black,
                        FlatStyle = FlatStyle.Flat,
                        TextAlign = ContentAlignment.MiddleCenter,
                        Margin = new Padding(5, 2, 5, 2),
                        Tag = product,
                        Cursor = Cursors.Hand
                    };

                    productButton.FlatAppearance.BorderColor = ColorTranslator.FromHtml(category.Color ?? "#3498db");
                    productButton.FlatAppearance.BorderSize = 1;
                    productButton.Click += ProductButton_Click;

                    _productsPanel.Controls.Add(productButton);
                }
            }
        }

        private void SetupEventHandlers()
        {
            // معالج الباركود
            _barcodeTextBox.KeyPress += BarcodeTextBox_KeyPress;
            
            // معالج تحرير الجدول
            _salesGrid.CellValueChanged += SalesGrid_CellValueChanged;
            _salesGrid.KeyDown += SalesGrid_KeyDown;
            
            // تحديث الوقت وحالة المزامنة
            var timer = new System.Windows.Forms.Timer { Interval = 1000 };
            timer.Tick += async (s, e) =>
            {
                _dateTimeLabel.Text = DateTime.Now.ToString("yyyy/MM/dd HH:mm");
                await UpdateSyncStatus();
            };
            timer.Start();
        }

        // Event Handlers
        private async void BarcodeTextBox_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                var barcode = _barcodeTextBox.Text.Trim();
                if (!string.IsNullOrEmpty(barcode))
                {
                    await AddProductByBarcode(barcode);
                    _barcodeTextBox.Clear();
                }
                e.Handled = true;
            }
        }

        private void ProductButton_Click(object sender, EventArgs e)
        {
            if (sender is Button button && button.Tag is Product product)
            {
                AddProductToInvoice(product, 1);
            }
        }

        private void SalesGrid_CellValueChanged(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0 && e.ColumnIndex >= 0)
            {
                var columnName = _salesGrid.Columns[e.ColumnIndex].Name;

                if (columnName == "Quantity" || columnName == "UnitPrice" || columnName == "Discount")
                {
                    UpdateInvoiceItemFromGrid(e.RowIndex);
                }
            }
        }

        private void SalesGrid_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Delete && _salesGrid.SelectedRows.Count > 0)
            {
                DeleteSelectedItem();
            }
        }

        private async void SaveInvoice_Click(object sender, EventArgs e)
        {
            await SaveCurrentInvoice();
        }

        private async void PrintInvoice_Click(object sender, EventArgs e)
        {
            await PrintCurrentInvoice();
        }

        private void DeleteItem_Click(object sender, EventArgs e)
        {
            DeleteSelectedItem();
        }

        private void NewInvoice_Click(object sender, EventArgs e)
        {
            CreateNewInvoice();
        }

        private async void Logout_Click(object sender, EventArgs e)
        {
            await LogoutUser();
        }

        private async void SyncData_Click(object sender, EventArgs e)
        {
            await SyncOfflineData();
        }

        private void RefreshSalesGrid()
        {
            _salesGrid.Rows.Clear();

            foreach (var item in _currentInvoice.Items)
            {
                var row = new DataGridViewRow();
                row.CreateCells(_salesGrid);

                row.Cells[0].Value = item.ProductName;
                row.Cells[1].Value = item.Quantity;
                row.Cells[2].Value = item.UnitPrice;
                row.Cells[3].Value = item.DiscountAmount;
                row.Cells[4].Value = item.Total;
                row.Tag = item;

                _salesGrid.Rows.Add(row);
            }
        }

        private void UpdateSummary()
        {
            _currentInvoice.CalculateTotal();

            var subTotalLabel = _summaryPanel.Controls.Find("المجموعالفرعيValue", true).FirstOrDefault() as Label;
            var discountLabel = _summaryPanel.Controls.Find("الخصمValue", true).FirstOrDefault() as Label;
            var taxLabel = _summaryPanel.Controls.Find("الضريبةValue", true).FirstOrDefault() as Label;
            var totalLabel = _summaryPanel.Controls.Find("الإجماليValue", true).FirstOrDefault() as Label;
            var paidLabel = _summaryPanel.Controls.Find("المدفوعValue", true).FirstOrDefault() as Label;
            var changeLabel = _summaryPanel.Controls.Find("الباقيValue", true).FirstOrDefault() as Label;

            if (subTotalLabel != null) subTotalLabel.Text = $"{_currentInvoice.SubTotal:F2} ر.س";
            if (discountLabel != null) discountLabel.Text = $"{_currentInvoice.DiscountAmount:F2} ر.س";
            if (taxLabel != null) taxLabel.Text = $"{_currentInvoice.TaxAmount:F2} ر.س";
            if (totalLabel != null) totalLabel.Text = $"{_currentInvoice.Total:F2} ر.س";
            if (paidLabel != null) paidLabel.Text = $"{_currentInvoice.PaidAmount:F2} ر.س";
            if (changeLabel != null) changeLabel.Text = $"{_currentInvoice.ChangeAmount:F2} ر.س";
        }

        // Helper Methods
        private async Task AddProductByBarcode(string barcode)
        {
            try
            {
                Product? product = null;

                // البحث بالباركود أولاً
                if (barcode.Length > 5)
                {
                    product = await _productService.GetProductByBarcodeAsync(barcode);
                }

                // إذا لم يوجد، البحث بالكود
                if (product == null)
                {
                    product = await _productService.GetProductByCodeAsync(barcode);
                }

                if (product != null)
                {
                    AddProductToInvoice(product, 1);
                }
                else
                {
                    MessageBox.Show("لم يتم العثور على المنتج", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث عن المنتج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void AddProductToInvoice(Product product, decimal quantity)
        {
            try
            {
                // التحقق من الصلاحيات إذا كان السعر مختلف
                var currentUser = _userService.GetCurrentUser();

                _currentInvoice.AddItem(product, quantity);
                RefreshSalesGrid();
                UpdateSummary();

                // تركيز على حقل الباركود
                _barcodeTextBox.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة المنتج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateInvoiceItemFromGrid(int rowIndex)
        {
            try
            {
                if (rowIndex >= _currentInvoice.Items.Count) return;

                var item = _currentInvoice.Items[rowIndex];
                var row = _salesGrid.Rows[rowIndex];

                // تحديث الكمية
                if (decimal.TryParse(row.Cells[1].Value?.ToString(), out decimal quantity))
                {
                    item.Quantity = quantity;
                }

                // تحديث السعر (مع التحقق من الصلاحيات)
                if (decimal.TryParse(row.Cells[2].Value?.ToString(), out decimal unitPrice))
                {
                    if (unitPrice != item.UnitPrice)
                    {
                        if (!_userService.HasPermission("editprices"))
                        {
                            MessageBox.Show("ليس لديك صلاحية لتعديل الأسعار", "تنبيه",
                                MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            row.Cells[2].Value = item.UnitPrice;
                            return;
                        }
                    }
                    item.UnitPrice = unitPrice;
                }

                // تحديث الخصم (مع التحقق من الصلاحيات)
                if (decimal.TryParse(row.Cells[3].Value?.ToString(), out decimal discount))
                {
                    if (discount > 0 && !_userService.HasPermission("applydiscounts"))
                    {
                        MessageBox.Show("ليس لديك صلاحية لتطبيق الخصومات", "تنبيه",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        row.Cells[3].Value = 0;
                        return;
                    }
                    item.DiscountAmount = discount;
                }

                item.CalculateTotal();
                row.Cells[4].Value = item.Total;

                UpdateSummary();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحديث العنصر: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void DeleteSelectedItem()
        {
            try
            {
                if (_salesGrid.SelectedRows.Count == 0)
                {
                    MessageBox.Show("يرجى اختيار عنصر للحذف", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                if (!_userService.HasPermission("deleteitems"))
                {
                    MessageBox.Show("ليس لديك صلاحية لحذف العناصر", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var result = MessageBox.Show("هل أنت متأكد من حذف هذا العنصر؟", "تأكيد الحذف",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    var rowIndex = _salesGrid.SelectedRows[0].Index;
                    if (rowIndex < _currentInvoice.Items.Count)
                    {
                        _currentInvoice.Items.RemoveAt(rowIndex);
                        RefreshSalesGrid();
                        UpdateSummary();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف العنصر: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task SaveCurrentInvoice()
        {
            try
            {
                if (_currentInvoice.Items.Count == 0)
                {
                    MessageBox.Show("لا يمكن حفظ فاتورة فارغة", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // طلب المبلغ المدفوع
                var paymentForm = new PaymentForm(_currentInvoice.Total);
                if (paymentForm.ShowDialog() == DialogResult.OK)
                {
                    _currentInvoice.PaidAmount = paymentForm.PaidAmount;
                    _currentInvoice.PaymentMethod = paymentForm.PaymentMethod;
                    _currentInvoice.Status = InvoiceStatus.Completed;

                    var success = await _invoiceService.SaveInvoiceAsync(_currentInvoice);

                    if (success)
                    {
                        MessageBox.Show("تم حفظ الفاتورة بنجاح", "نجح",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);

                        UpdateSummary();

                        // سؤال عن الطباعة
                        var printResult = MessageBox.Show("هل تريد طباعة الفاتورة؟", "طباعة",
                            MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                        if (printResult == DialogResult.Yes)
                        {
                            await PrintCurrentInvoice();
                        }
                    }
                    else
                    {
                        MessageBox.Show("فشل في حفظ الفاتورة", "خطأ",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الفاتورة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task PrintCurrentInvoice()
        {
            try
            {
                if (_currentInvoice.Items.Count == 0)
                {
                    MessageBox.Show("لا يمكن طباعة فاتورة فارغة", "تنبيه",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var success = await _invoiceService.PrintInvoiceAsync(_currentInvoice);

                if (success)
                {
                    MessageBox.Show("تم طباعة الفاتورة بنجاح", "نجح",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("فشل في طباعة الفاتورة", "خطأ",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة الفاتورة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CreateNewInvoice()
        {
            try
            {
                if (_currentInvoice.Items.Count > 0)
                {
                    var result = MessageBox.Show("هناك فاتورة حالية غير محفوظة. هل تريد المتابعة؟",
                        "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                    if (result != DialogResult.Yes)
                        return;
                }

                InitializeNewInvoice();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء فاتورة جديدة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task LogoutUser()
        {
            try
            {
                var result = MessageBox.Show("هل أنت متأكد من تسجيل الخروج؟", "تأكيد",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    await _userService.LogoutAsync();
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تسجيل الخروج: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async Task UpdateSyncStatus()
        {
            try
            {
                var isOnline = await _offlineDataService.IsOnlineAsync();
                var pendingCount = await _offlineDataService.GetPendingInvoicesCountAsync();

                if (isOnline)
                {
                    if (pendingCount > 0)
                    {
                        _syncStatusLabel.Text = $"🔄 مزامنة ({pendingCount} معلق)";
                        _syncStatusLabel.ForeColor = Color.Orange;

                        // محاولة مزامنة تلقائية
                        _ = Task.Run(async () => await _offlineDataService.SyncOfflineDataAsync());
                    }
                    else
                    {
                        _syncStatusLabel.Text = "✅ متصل";
                        _syncStatusLabel.ForeColor = Color.LightGreen;
                    }
                }
                else
                {
                    if (pendingCount > 0)
                    {
                        _syncStatusLabel.Text = $"❌ غير متصل ({pendingCount} معلق)";
                        _syncStatusLabel.ForeColor = Color.Red;
                    }
                    else
                    {
                        _syncStatusLabel.Text = "❌ غير متصل";
                        _syncStatusLabel.ForeColor = Color.Red;
                    }
                }
            }
            catch (Exception)
            {
                _syncStatusLabel.Text = "⚠️ خطأ في الحالة";
                _syncStatusLabel.ForeColor = Color.Yellow;
            }
        }

        private async Task SyncOfflineData()
        {
            try
            {
                var pendingCount = await _offlineDataService.GetPendingInvoicesCountAsync();

                if (pendingCount == 0)
                {
                    MessageBox.Show("لا توجد بيانات معلقة للمزامنة", "معلومات",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                var result = MessageBox.Show($"سيتم مزامنة {pendingCount} فاتورة معلقة. هل تريد المتابعة؟",
                    "تأكيد المزامنة", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result != DialogResult.Yes) return;

                // تعطيل الواجهة أثناء المزامنة
                this.Enabled = false;
                _syncStatusLabel.Text = "🔄 جاري المزامنة...";
                _syncStatusLabel.ForeColor = Color.Orange;

                var success = await _offlineDataService.SyncOfflineDataAsync();

                if (success)
                {
                    MessageBox.Show("تم إرسال جميع البيانات بنجاح", "نجحت المزامنة",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    var remainingCount = await _offlineDataService.GetPendingInvoicesCountAsync();
                    MessageBox.Show($"تم إرسال بعض البيانات. يتبقى {remainingCount} فاتورة",
                        "مزامنة جزئية", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في المزامنة: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                this.Enabled = true;
                await UpdateSyncStatus();
            }
        }
    }
}
