@echo off
chcp 65001 > nul
title نظام نقطة البيع - تشغيل مبسط

echo ========================================
echo        نظام نقطة البيع الاحترافي
echo              تشغيل مبسط
echo ========================================
echo.

echo 🔍 جاري التحقق من .NET...
echo.

REM التحقق من وجود .NET Runtime
where dotnet > nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ .NET غير مثبت
    echo.
    echo 📥 يرجى تثبيت .NET SDK من الرابط المفتوح
    echo.
    echo بعد التثبيت:
    echo 1. أعد تشغيل الكمبيوتر
    echo 2. شغل هذا الملف مرة أخرى
    echo.
    pause
    exit /b 1
)

echo ✅ .NET متاح
echo.

REM محاولة تشغيل النظام
echo 🚀 جاري تشغيل النظام...
echo.

REM إنشاء ملف تشغيل مؤقت
echo using System; > temp_pos.cs
echo using System.Windows.Forms; >> temp_pos.cs
echo using System.Drawing; >> temp_pos.cs
echo. >> temp_pos.cs
echo namespace TempPOS >> temp_pos.cs
echo { >> temp_pos.cs
echo     public partial class MainForm : Form >> temp_pos.cs
echo     { >> temp_pos.cs
echo         public MainForm() >> temp_pos.cs
echo         { >> temp_pos.cs
echo             InitializeComponent(); >> temp_pos.cs
echo         } >> temp_pos.cs
echo. >> temp_pos.cs
echo         private void InitializeComponent() >> temp_pos.cs
echo         { >> temp_pos.cs
echo             this.Text = "نظام نقطة البيع - الإصدار التجريبي"; >> temp_pos.cs
echo             this.Size = new Size(800, 600); >> temp_pos.cs
echo             this.StartPosition = FormStartPosition.CenterScreen; >> temp_pos.cs
echo             this.RightToLeft = RightToLeft.Yes; >> temp_pos.cs
echo             this.RightToLeftLayout = true; >> temp_pos.cs
echo. >> temp_pos.cs
echo             var label = new Label(); >> temp_pos.cs
echo             label.Text = "🏪 نظام نقطة البيع الاحترافي\\n\\n"; >> temp_pos.cs
echo             label.Text += "✅ تم تثبيت .NET بنجاح!\\n\\n"; >> temp_pos.cs
echo             label.Text += "للحصول على النظام الكامل:\\n"; >> temp_pos.cs
echo             label.Text += "1. ثبت .NET SDK\\n"; >> temp_pos.cs
echo             label.Text += "2. شغل ملف 'تشغيل_النظام.bat'\\n\\n"; >> temp_pos.cs
echo             label.Text += "المميزات الكاملة:\\n"; >> temp_pos.cs
echo             label.Text += "• ضرائب متغيرة لكل منتج\\n"; >> temp_pos.cs
echo             label.Text += "• عمل بدون اتصال\\n"; >> temp_pos.cs
echo             label.Text += "• طباعة احترافية\\n"; >> temp_pos.cs
echo             label.Text += "• نظام صلاحيات متقدم"; >> temp_pos.cs
echo             label.Font = new Font("Segoe UI", 12); >> temp_pos.cs
echo             label.Size = new Size(700, 400); >> temp_pos.cs
echo             label.Location = new Point(50, 50); >> temp_pos.cs
echo             label.TextAlign = ContentAlignment.TopCenter; >> temp_pos.cs
echo             this.Controls.Add(label); >> temp_pos.cs
echo. >> temp_pos.cs
echo             var button = new Button(); >> temp_pos.cs
echo             button.Text = "إغلاق"; >> temp_pos.cs
echo             button.Size = new Size(100, 40); >> temp_pos.cs
echo             button.Location = new Point(350, 500); >> temp_pos.cs
echo             button.Click += (s, e) =^> this.Close(); >> temp_pos.cs
echo             this.Controls.Add(button); >> temp_pos.cs
echo         } >> temp_pos.cs
echo     } >> temp_pos.cs
echo. >> temp_pos.cs
echo     class Program >> temp_pos.cs
echo     { >> temp_pos.cs
echo         [STAThread] >> temp_pos.cs
echo         static void Main() >> temp_pos.cs
echo         { >> temp_pos.cs
echo             Application.EnableVisualStyles(); >> temp_pos.cs
echo             Application.SetCompatibleTextRenderingDefault(false); >> temp_pos.cs
echo             Application.Run(new MainForm()); >> temp_pos.cs
echo         } >> temp_pos.cs
echo     } >> temp_pos.cs
echo } >> temp_pos.cs

REM محاولة تشغيل النسخة المبسطة
echo 📱 تشغيل النسخة التجريبية...
csc /target:winexe /reference:System.Windows.Forms.dll /reference:System.Drawing.dll temp_pos.cs 2>nul
if exist temp_pos.exe (
    echo ✅ تم إنشاء النسخة التجريبية
    start temp_pos.exe
    timeout /t 2 >nul
    del temp_pos.cs temp_pos.exe 2>nul
) else (
    echo ❌ فشل في إنشاء النسخة التجريبية
    echo.
    echo يرجى تثبيت .NET SDK للحصول على النظام الكامل
)

echo.
echo 📋 للحصول على النظام الكامل:
echo 1. ثبت .NET SDK من الرابط المفتوح
echo 2. أعد تشغيل الكمبيوتر  
echo 3. شغل ملف "تشغيل_النظام.bat"
echo.
pause
