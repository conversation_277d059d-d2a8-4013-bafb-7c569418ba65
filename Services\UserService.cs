using POSSystem.Models;

namespace POSSystem.Services
{
    /// <summary>
    /// خدمة المستخدمين
    /// </summary>
    public class UserService : IUserService
    {
        private readonly IApiService _apiService;
        private User? _currentUser;

        public UserService(IApiService apiService)
        {
            _apiService = apiService;
        }

        public async Task<LoginResponse> LoginAsync(LoginRequest request)
        {
            try
            {
                var response = await _apiService.PostAsync<LoginResponse>("auth/login", request);
                
                if (response != null && response.Success && response.User != null)
                {
                    _currentUser = response.User;
                    
                    if (!string.IsNullOrEmpty(response.Token))
                    {
                        _apiService.SetAuthToken(response.Token);
                    }
                    
                    // حفظ معلومات المستخدم محلياً
                    SaveUserSession(response);
                }
                
                return response ?? new LoginResponse 
                { 
                    Success = false, 
                    Message = "فشل في الاتصال بالخادم" 
                };
            }
            catch (Exception ex)
            {
                return new LoginResponse 
                { 
                    Success = false, 
                    Message = $"خطأ في تسجيل الدخول: {ex.Message}" 
                };
            }
        }

        public async Task<bool> LogoutAsync()
        {
            try
            {
                await _apiService.PostAsync<object>("auth/logout", new { });
                
                _currentUser = null;
                _apiService.ClearAuthToken();
                ClearUserSession();
                
                return true;
            }
            catch (Exception)
            {
                // حتى لو فشل في الاتصال بالخادم، نقوم بتسجيل الخروج محلياً
                _currentUser = null;
                _apiService.ClearAuthToken();
                ClearUserSession();
                
                return true;
            }
        }

        public User? GetCurrentUser()
        {
            if (_currentUser == null)
            {
                // محاولة استرجاع المستخدم من الجلسة المحفوظة
                _currentUser = LoadUserSession();
            }
            
            return _currentUser;
        }

        public bool HasPermission(string permission)
        {
            var user = GetCurrentUser();
            if (user?.Permissions == null) return false;

            return permission.ToLower() switch
            {
                "editprices" => user.Permissions.CanEditPrices,
                "applydiscounts" => user.Permissions.CanApplyDiscounts,
                "deleteitems" => user.Permissions.CanDeleteItems,
                "voidinvoices" => user.Permissions.CanVoidInvoices,
                "accessreports" => user.Permissions.CanAccessReports,
                "manageusers" => user.Permissions.CanManageUsers,
                "manageproducts" => user.Permissions.CanManageProducts,
                "closecash" => user.Permissions.CanCloseCash,
                "opencash" => user.Permissions.CanOpenCash,
                "printreports" => user.Permissions.CanPrintReports,
                _ => false
            };
        }

        public async Task<bool> UpdateUserAsync(User user)
        {
            try
            {
                var response = await _apiService.PutAsync<User>($"users/{user.Id}", user);
                
                if (response != null && _currentUser?.Id == user.Id)
                {
                    _currentUser = response;
                    SaveUserSession(new LoginResponse { User = response, Success = true });
                }
                
                return response != null;
            }
            catch (Exception)
            {
                return false;
            }
        }

        private void SaveUserSession(LoginResponse loginResponse)
        {
            try
            {
                var sessionData = new
                {
                    User = loginResponse.User,
                    Token = loginResponse.Token,
                    ExpiresAt = loginResponse.ExpiresAt,
                    LoginTime = DateTime.Now
                };

                var json = Newtonsoft.Json.JsonConvert.SerializeObject(sessionData);
                var filePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "POSSystem", "session.json");
                
                Directory.CreateDirectory(Path.GetDirectoryName(filePath)!);
                File.WriteAllText(filePath, json);
            }
            catch (Exception)
            {
                // تجاهل أخطاء حفظ الجلسة
            }
        }

        private User? LoadUserSession()
        {
            try
            {
                var filePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "POSSystem", "session.json");
                
                if (!File.Exists(filePath)) return null;
                
                var json = File.ReadAllText(filePath);
                var sessionData = Newtonsoft.Json.JsonConvert.DeserializeAnonymousType(json, new
                {
                    User = (User?)null,
                    Token = "",
                    ExpiresAt = (DateTime?)null,
                    LoginTime = DateTime.MinValue
                });

                // التحقق من انتهاء صلاحية الجلسة
                if (sessionData?.ExpiresAt.HasValue == true && sessionData.ExpiresAt.Value < DateTime.Now)
                {
                    ClearUserSession();
                    return null;
                }

                // التحقق من مدة الجلسة (8 ساعات كحد أقصى)
                if (sessionData?.LoginTime != null && DateTime.Now.Subtract(sessionData.LoginTime).TotalHours > 8)
                {
                    ClearUserSession();
                    return null;
                }

                if (!string.IsNullOrEmpty(sessionData?.Token))
                {
                    _apiService.SetAuthToken(sessionData.Token);
                }

                return sessionData?.User;
            }
            catch (Exception)
            {
                return null;
            }
        }

        private void ClearUserSession()
        {
            try
            {
                var filePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "POSSystem", "session.json");
                
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }
            }
            catch (Exception)
            {
                // تجاهل أخطاء حذف الجلسة
            }
        }
    }
}
