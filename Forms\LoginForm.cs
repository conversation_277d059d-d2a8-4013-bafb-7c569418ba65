using POSSystem.Models;
using POSSystem.Services;
using Microsoft.Extensions.DependencyInjection;

namespace POSSystem.Forms
{
    public partial class LoginForm : Form
    {
        private readonly IUserService _userService;
        private readonly IServiceProvider _serviceProvider;
        private int _loginAttempts = 0;
        private const int MaxLoginAttempts = 3;

        public LoginForm(IUserService userService, IServiceProvider serviceProvider)
        {
            _userService = userService;
            _serviceProvider = serviceProvider;
            InitializeComponent();
            SetupForm();
        }

        private void SetupForm()
        {
            // إعداد النموذج
            this.Text = "تسجيل الدخول - نظام نقطة البيع";
            this.Size = new Size(450, 350);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.FromArgb(240, 248, 255);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // التحقق من وجود جلسة مفتوحة
            var currentUser = _userService.GetCurrentUser();
            if (currentUser != null)
            {
                OpenMainForm();
                return;
            }

            CreateControls();
        }

        private void CreateControls()
        {
            // لوحة رئيسية
            var mainPanel = new Panel
            {
                Size = new Size(400, 280),
                Location = new Point(25, 25),
                BackColor = Color.White,
                BorderStyle = BorderStyle.None
            };

            // إضافة ظل للوحة
            mainPanel.Paint += (s, e) =>
            {
                var rect = new Rectangle(0, 0, mainPanel.Width - 1, mainPanel.Height - 1);
                e.Graphics.DrawRectangle(new Pen(Color.FromArgb(200, 200, 200)), rect);
            };

            // عنوان التطبيق
            var titleLabel = new Label
            {
                Text = "نظام نقطة البيع",
                Font = new Font("Segoe UI", 18, FontStyle.Bold),
                ForeColor = Color.FromArgb(41, 128, 185),
                Size = new Size(300, 40),
                Location = new Point(50, 30),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // أيقونة المستخدم
            var userIcon = new Label
            {
                Text = "👤",
                Font = new Font("Segoe UI", 24),
                Size = new Size(50, 50),
                Location = new Point(175, 80),
                TextAlign = ContentAlignment.MiddleCenter,
                ForeColor = Color.FromArgb(52, 152, 219)
            };

            // حقل اسم المستخدم
            var usernameLabel = new Label
            {
                Text = "اسم المستخدم:",
                Font = new Font("Segoe UI", 10),
                Size = new Size(100, 25),
                Location = new Point(280, 140),
                TextAlign = ContentAlignment.MiddleRight
            };

            var usernameTextBox = new TextBox
            {
                Name = "usernameTextBox",
                Font = new Font("Segoe UI", 11),
                Size = new Size(200, 30),
                Location = new Point(70, 140),
                BorderStyle = BorderStyle.FixedSingle
            };

            // حقل كلمة المرور
            var passwordLabel = new Label
            {
                Text = "كلمة المرور:",
                Font = new Font("Segoe UI", 10),
                Size = new Size(100, 25),
                Location = new Point(280, 180),
                TextAlign = ContentAlignment.MiddleRight
            };

            var passwordTextBox = new TextBox
            {
                Name = "passwordTextBox",
                Font = new Font("Segoe UI", 11),
                Size = new Size(200, 30),
                Location = new Point(70, 180),
                BorderStyle = BorderStyle.FixedSingle,
                UseSystemPasswordChar = true
            };

            // زر تسجيل الدخول
            var loginButton = new Button
            {
                Name = "loginButton",
                Text = "تسجيل الدخول",
                Font = new Font("Segoe UI", 11, FontStyle.Bold),
                Size = new Size(120, 35),
                Location = new Point(140, 220),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            loginButton.FlatAppearance.BorderSize = 0;
            loginButton.Click += LoginButton_Click;

            // رسالة الحالة
            var statusLabel = new Label
            {
                Name = "statusLabel",
                Text = "",
                Font = new Font("Segoe UI", 9),
                Size = new Size(300, 20),
                Location = new Point(50, 260),
                TextAlign = ContentAlignment.MiddleCenter,
                ForeColor = Color.Red
            };

            // إضافة الكنترولز للوحة
            mainPanel.Controls.AddRange(new Control[]
            {
                titleLabel, userIcon, usernameLabel, usernameTextBox,
                passwordLabel, passwordTextBox, loginButton, statusLabel
            });

            // إضافة اللوحة للنموذج
            this.Controls.Add(mainPanel);

            // تعيين التركيز على حقل اسم المستخدم
            usernameTextBox.Focus();

            // إضافة معالج Enter للحقول
            usernameTextBox.KeyPress += (s, e) =>
            {
                if (e.KeyChar == (char)Keys.Enter)
                {
                    passwordTextBox.Focus();
                    e.Handled = true;
                }
            };

            passwordTextBox.KeyPress += (s, e) =>
            {
                if (e.KeyChar == (char)Keys.Enter)
                {
                    LoginButton_Click(loginButton, EventArgs.Empty);
                    e.Handled = true;
                }
            };
        }

        private async void LoginButton_Click(object sender, EventArgs e)
        {
            var usernameTextBox = this.Controls.Find("usernameTextBox", true).FirstOrDefault() as TextBox;
            var passwordTextBox = this.Controls.Find("passwordTextBox", true).FirstOrDefault() as TextBox;
            var statusLabel = this.Controls.Find("statusLabel", true).FirstOrDefault() as Label;
            var loginButton = sender as Button;

            if (usernameTextBox == null || passwordTextBox == null || statusLabel == null || loginButton == null)
                return;

            var username = usernameTextBox.Text.Trim();
            var password = passwordTextBox.Text;

            // التحقق من صحة البيانات
            if (string.IsNullOrEmpty(username))
            {
                statusLabel.Text = "يرجى إدخال اسم المستخدم";
                statusLabel.ForeColor = Color.Red;
                usernameTextBox.Focus();
                return;
            }

            if (string.IsNullOrEmpty(password))
            {
                statusLabel.Text = "يرجى إدخال كلمة المرور";
                statusLabel.ForeColor = Color.Red;
                passwordTextBox.Focus();
                return;
            }

            // تعطيل الزر أثناء المعالجة
            loginButton.Enabled = false;
            loginButton.Text = "جاري التحقق...";
            statusLabel.Text = "جاري التحقق من البيانات...";
            statusLabel.ForeColor = Color.Blue;

            try
            {
                var loginRequest = new LoginRequest
                {
                    Username = username,
                    Password = password
                };

                var response = await _userService.LoginAsync(loginRequest);

                if (response.Success && response.User != null)
                {
                    statusLabel.Text = "تم تسجيل الدخول بنجاح";
                    statusLabel.ForeColor = Color.Green;
                    
                    // فتح النموذج الرئيسي
                    await Task.Delay(500); // تأخير قصير لإظهار رسالة النجاح
                    OpenMainForm();
                }
                else
                {
                    _loginAttempts++;
                    statusLabel.Text = response.Message ?? "فشل في تسجيل الدخول";
                    statusLabel.ForeColor = Color.Red;

                    if (_loginAttempts >= MaxLoginAttempts)
                    {
                        statusLabel.Text = "تم تجاوز عدد المحاولات المسموح. سيتم إغلاق التطبيق.";
                        await Task.Delay(2000);
                        Application.Exit();
                        return;
                    }

                    passwordTextBox.Clear();
                    passwordTextBox.Focus();
                }
            }
            catch (Exception ex)
            {
                statusLabel.Text = $"خطأ في الاتصال: {ex.Message}";
                statusLabel.ForeColor = Color.Red;
            }
            finally
            {
                loginButton.Enabled = true;
                loginButton.Text = "تسجيل الدخول";
            }
        }

        private void OpenMainForm()
        {
            try
            {
                var mainForm = _serviceProvider.GetRequiredService<MainPOSForm>();
                this.Hide();
                
                mainForm.FormClosed += (s, e) =>
                {
                    this.Show();
                    // إعادة تعيين النموذج
                    var usernameTextBox = this.Controls.Find("usernameTextBox", true).FirstOrDefault() as TextBox;
                    var passwordTextBox = this.Controls.Find("passwordTextBox", true).FirstOrDefault() as TextBox;
                    var statusLabel = this.Controls.Find("statusLabel", true).FirstOrDefault() as Label;
                    
                    if (usernameTextBox != null) usernameTextBox.Clear();
                    if (passwordTextBox != null) passwordTextBox.Clear();
                    if (statusLabel != null) statusLabel.Text = "";
                    
                    _loginAttempts = 0;
                };
                
                mainForm.Show();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح النموذج الرئيسي: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
