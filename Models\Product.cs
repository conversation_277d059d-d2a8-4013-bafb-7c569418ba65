using System.ComponentModel.DataAnnotations;

namespace POSSystem.Models
{
    /// <summary>
    /// نموذج المنتج
    /// </summary>
    public class Product
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(20)]
        public string Code { get; set; } = string.Empty;
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(100)]
        public string NameEnglish { get; set; } = string.Empty;
        
        [StringLength(500)]
        public string? Description { get; set; }
        
        [Required]
        public decimal Price { get; set; }
        
        public decimal CostPrice { get; set; }
        
        public decimal? DiscountPercentage { get; set; }
        
        public decimal? DiscountAmount { get; set; }
        
        public int CategoryId { get; set; }
        
        public Category? Category { get; set; }
        
        public string? Barcode { get; set; }
        
        public string? Image { get; set; }
        
        public ProductUnit Unit { get; set; } = ProductUnit.Piece;
        
        public bool IsActive { get; set; } = true;
        
        public bool IsWeighted { get; set; } = false;
        
        public decimal? MinStock { get; set; }
        
        public decimal CurrentStock { get; set; }
        
        public bool TrackStock { get; set; } = true;
        
        public decimal TaxRate { get; set; } = 0.15m;

        public bool IsTaxable { get; set; } = true;

        public bool IsTaxInclusive { get; set; } = false;
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        public DateTime? UpdatedAt { get; set; }
        
        /// <summary>
        /// السعر الأساسي بعد الخصم (بدون ضريبة)
        /// </summary>
        public decimal BasePrice
        {
            get
            {
                var price = Price;

                // إذا كان السعر شامل الضريبة، نحتاج لاستخراج السعر الأساسي
                if (IsTaxInclusive && IsTaxable && TaxRate > 0)
                {
                    price = price / (1 + TaxRate);
                }

                // تطبيق الخصومات
                if (DiscountPercentage.HasValue && DiscountPercentage > 0)
                {
                    price -= (price * DiscountPercentage.Value / 100);
                }

                if (DiscountAmount.HasValue && DiscountAmount > 0)
                {
                    price -= DiscountAmount.Value;
                }

                return Math.Max(0, price);
            }
        }

        /// <summary>
        /// قيمة الضريبة
        /// </summary>
        public decimal TaxAmount => IsTaxable ? (BasePrice * TaxRate) : 0;

        /// <summary>
        /// السعر النهائي شامل الضريبة
        /// </summary>
        public decimal FinalPrice => BasePrice + TaxAmount;

        /// <summary>
        /// السعر المعروض للعميل (حسب إعداد الضريبة)
        /// </summary>
        public decimal DisplayPrice => IsTaxInclusive ? FinalPrice : BasePrice;
    }

    /// <summary>
    /// وحدات المنتج
    /// </summary>
    public enum ProductUnit
    {
        Piece = 1,      // قطعة
        Kilogram = 2,   // كيلوجرام
        Gram = 3,       // جرام
        Liter = 4,      // لتر
        Meter = 5,      // متر
        Box = 6,        // صندوق
        Pack = 7        // عبوة
    }

    /// <summary>
    /// فئة المنتج
    /// </summary>
    public class Category
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;
        
        [StringLength(100)]
        public string NameEnglish { get; set; } = string.Empty;
        
        [StringLength(500)]
        public string? Description { get; set; }
        
        public string? Image { get; set; }
        
        public string? Color { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        public int SortOrder { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.Now;
        
        public List<Product> Products { get; set; } = new();
    }

    /// <summary>
    /// طلب البحث عن المنتجات
    /// </summary>
    public class ProductSearchRequest
    {
        public string? SearchTerm { get; set; }
        public string? Code { get; set; }
        public string? Barcode { get; set; }
        public int? CategoryId { get; set; }
        public bool? IsActive { get; set; } = true;
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 50;
    }
}
