using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Configuration;
using POSSystem.Services;
using POSSystem.Forms;

namespace POSSystem
{
    internal static class Program
    {
        /// <summary>
        ///  The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            // To customize application configuration such as set high DPI settings or default font,
            // see https://aka.ms/applicationconfiguration.
            ApplicationConfiguration.Initialize();

            // إعداد خدمات التطبيق
            var services = new ServiceCollection();
            ConfigureServices(services);
            
            var serviceProvider = services.BuildServiceProvider();

            // تشغيل التطبيق
            Application.Run(serviceProvider.GetRequiredService<LoginForm>());
        }

        private static void ConfigureServices(ServiceCollection services)
        {
            // إعداد التكوين
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .Build();

            services.AddSingleton<IConfiguration>(configuration);

            // إعداد HttpClient
            services.AddHttpClient();

            // إعداد الخدمات
            services.AddSingleton<IApiService, ApiService>();
            services.AddSingleton<IUserService, UserService>();
            services.AddSingleton<IProductService, ProductService>();
            services.AddSingleton<IInvoiceService, InvoiceService>();
            services.AddSingleton<IPrintService, PrintService>();

            // إعداد النماذج
            services.AddTransient<LoginForm>();
            services.AddTransient<MainPOSForm>();
        }
    }
}
